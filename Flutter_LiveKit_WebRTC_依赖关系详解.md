# Flutter LiveKit WebRTC 依赖关系详解

## 概述

本文档详细解释了 Flutter LiveKit 项目中 `flutter_webrtc: ^0.14.1` 与 iOS podspec 中 `s.dependency 'WebRTC-SDK', '125.6422.07'` 和 `s.dependency 'flutter_webrtc'` 的关系和作用。

## 1. 基本依赖关系

### 1.1 pubspec.yaml 中的依赖
```yaml
dependencies:
  flutter_webrtc: ^0.14.1
```

### 1.2 iOS podspec 中的依赖
```ruby
s.dependency 'WebRTC-SDK', '125.6422.07'
s.dependency 'flutter_webrtc'
```

## 2. flutter_webrtc 的多平台架构

### 2.1 架构概述

flutter_webrtc 是一个**联邦化插件**（Federated Plugin），包含所有平台的实现：

```
flutter_webrtc 包结构：
├── lib/                    # Dart 通用 API
├── ios/                    # iOS 平台实现
├── android/                # Android 平台实现  
├── macos/                  # macOS 平台实现
├── windows/                # Windows 平台实现
├── linux/                  # Linux 平台实现
└── web/                    # Web 平台实现
```

### 2.2 多平台架构图

```mermaid
graph TD
    A[pubspec.yaml<br/>flutter_webrtc: ^0.14.1] --> B[flutter_webrtc 总包]
    
    B --> C[iOS 实现<br/>flutter_webrtc/ios/]
    B --> D[Android 实现<br/>flutter_webrtc/android/]
    B --> E[macOS 实现<br/>flutter_webrtc/macos/]
    B --> F[Windows 实现<br/>flutter_webrtc/windows/]
    B --> G[Linux 实现<br/>flutter_webrtc/linux/]
    B --> H[Web 实现<br/>flutter_webrtc/web/]
    B --> I[Dart API<br/>flutter_webrtc/lib/]
    
    C --> C1[WebRTC-SDK iOS<br/>125.6422.07]
    D --> D1[webrtc-sdk Android<br/>125.6422.03]
    E --> E1[WebRTC-SDK macOS<br/>125.6422.07]
    F --> F1[libwebrtc Windows]
    G --> G1[libwebrtc Linux]
    H --> H1[Browser WebRTC API]
    
    J[LiveKit iOS Plugin] --> C
    K[LiveKit Android Plugin] --> D
    L[LiveKit macOS Plugin] --> E
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style I fill:#fff3e0
```

## 3. 两种 flutter_webrtc 依赖的区别

### 3.1 依赖层次对比

| 方面 | pubspec.yaml 依赖 | iOS podspec 依赖 |
|------|------------------|------------------|
| **层级** | Dart/Flutter 层 | iOS 原生层 |
| **管理器** | pub (Dart 包管理器) | CocoaPods (iOS 依赖管理器) |
| **作用域** | 整个 Flutter 应用 | 仅 iOS 平台 |
| **版本控制** | 明确指定版本范围 | 继承 pubspec 解析的版本 |
| **提供内容** | Dart API + 所有平台插件 | 仅 iOS 原生插件代码 |

### 3.2 依赖解析流程

```mermaid
graph TD
    A[LiveKit Flutter App] --> B[pubspec.yaml<br/>flutter_webrtc: ^0.14.1]
    B --> C[pub.dev<br/>下载 flutter_webrtc 包]
    C --> D[flutter_webrtc Dart API]
    C --> E[flutter_webrtc iOS Plugin]
    
    F[LiveKit iOS Plugin] --> G[livekit_client.podspec<br/>s.dependency 'flutter_webrtc']
    G --> E
    E --> H[WebRTC-SDK<br/>125.6422.07]
    
    D --> I[Dart 层 WebRTC 调用]
    E --> J[iOS 原生 WebRTC 实现]
    H --> K[Google WebRTC iOS Framework]
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style E fill:#fff3e0
    style H fill:#e8f5e8
```

## 4. iOS podspec 中双重依赖的原因

### 4.1 WebRTC-SDK vs flutter_webrtc 对比

| 方面 | WebRTC-SDK | flutter_webrtc |
|------|------------|----------------|
| **类型** | 原生 iOS 框架 | Flutter 插件的 iOS 部分 |
| **功能** | 底层 WebRTC 实现 | Dart ↔ iOS 桥接 |
| **版本管理** | 明确指定版本 | 继承 Flutter 依赖版本 |
| **直接使用** | LiveKit 可直接调用 | 主要为 flutter_webrtc 服务 |
| **依赖关系** | 被 flutter_webrtc 依赖 | 依赖 WebRTC-SDK |

### 4.2 双重依赖架构图

```mermaid
graph TD
    subgraph "LiveKit iOS Plugin"
        A[LiveKitPlugin.swift]
    end
    
    subgraph "两个依赖的不同作用"
        B[flutter_webrtc 依赖]
        C[WebRTC-SDK 依赖]
    end
    
    A --> B
    A --> C
    
    B --> D[FlutterWebRTCPlugin.sharedSingleton<br/>- 访问已创建的 tracks<br/>- 与 Flutter 层协调<br/>- 复用标准封装]
    
    C --> E[直接使用 WebRTC 原生 API<br/>- RTCAudioSession 配置<br/>- RTCAudioTrack 直接操作<br/>- 音频数据流处理]
    
    subgraph "实际代码示例"
        F["// 通过 flutter_webrtc 获取对象<br/>let webrtc = FlutterWebRTCPlugin.sharedSingleton()<br/>let localTrack = webrtc?.localTracks![trackId]"]
        
        G["// 直接使用 WebRTC-SDK<br/>let config = RTCAudioSessionConfiguration.webRTC()<br/>let session = RTCAudioSession.sharedInstance()"]
    end
    
    D --> F
    E --> G
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
```

### 4.3 具体使用场景

#### flutter_webrtc 依赖的作用：
```swift
import flutter_webrtc

// 获取 flutter_webrtc 的单例实例
let webrtc = FlutterWebRTCPlugin.sharedSingleton()

// 访问 flutter_webrtc 管理的 tracks
let localTrack = webrtc?.localTracks![unwrappedTrackId]
let track = webrtc?.remoteTrack(forId: unwrappedTrackId)
```

#### WebRTC-SDK 依赖的作用：
```swift
import WebRTC

// 直接使用 WebRTC-SDK 的原生类型
let configuration = RTCAudioSessionConfiguration.webRTC()
let rtcSession = RTCAudioSession.sharedInstance()

// 处理原生 WebRTC 对象
if let audioTrack = track as? RTCAudioTrack {
    // 直接操作 RTCAudioTrack
}
```

## 5. Flutter iOS LiveKit 完整架构

### 5.1 完整架构图

```mermaid
graph TB
    subgraph "Flutter App Layer"
        A[Flutter App] --> B[LiveKit Dart API]
        B --> C[Room Management]
        B --> D[Participant Management]
        B --> E[Track Management]
    end

    subgraph "LiveKit Dart SDK Layer"
        C --> F[Room Class]
        D --> G[LocalParticipant/RemoteParticipant]
        E --> H[LocalTrack/RemoteTrack]

        F --> I[Engine]
        I --> J[SignalClient WebSocket]
        I --> K[Transport PeerConnection]

        H --> L[VideoTrackRenderer Widget]
        H --> M[AudioTrack Processing]
    end

    subgraph "Flutter WebRTC Bridge Layer"
        K --> N[flutter_webrtc Dart API]
        L --> N
        M --> N

        N --> O[Method Channel]
        O --> P[flutter_webrtc iOS Plugin]
    end

    subgraph "iOS Native Layer"
        P --> Q[FlutterWebRTCPlugin.swift]
        Q --> R[RTCPeerConnection Wrapper]
        Q --> S[RTCVideoRenderer Wrapper]
        Q --> T[RTCAudioTrack Wrapper]

        subgraph "LiveKit iOS Plugin"
            U[LiveKitPlugin.swift] --> V[Audio Processing]
            U --> W[FFT Processor]
            U --> X[Native Audio Management]
        end

        P --> U
    end

    subgraph "WebRTC SDK Layer"
        R --> Y[WebRTC-SDK 125.6422.07]
        S --> Y
        T --> Y
        V --> Y

        Y --> Z[RTCPeerConnection]
        Y --> AA[RTCVideoTrack]
        Y --> BB[RTCAudioTrack]
        Y --> CC[RTCDataChannel]
    end

    subgraph "iOS System Layer"
        Z --> DD[Network Stack]
        AA --> EE[AVFoundation Video]
        BB --> FF[AVFoundation Audio]
        CC --> DD

        EE --> GG[Camera/Screen Capture]
        FF --> HH[Microphone/Speaker]
        DD --> II[Network Interfaces]
    end

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style N fill:#fff3e0
    style Y fill:#e8f5e8
    style U fill:#fce4ec
```

### 5.2 数据流向图

```mermaid
sequenceDiagram
    participant App as Flutter App
    participant LK as LiveKit Dart
    participant FW as flutter_webrtc
    participant MC as Method Channel
    participant iOS as iOS Plugin
    participant WTC as WebRTC-SDK
    participant Sys as iOS System

    App->>LK: room.connect()
    LK->>FW: createPeerConnection()
    FW->>MC: invokeMethod("createPeerConnection")
    MC->>iOS: FlutterWebRTCPlugin
    iOS->>WTC: RTCPeerConnection.init()
    WTC->>Sys: Network/Audio/Video APIs

    Note over App,Sys: 建立连接

    App->>LK: localParticipant.publishTrack()
    LK->>FW: addTrack()
    FW->>MC: invokeMethod("addTrack")
    MC->>iOS: addTrack wrapper
    iOS->>WTC: peerConnection.add(track)
    WTC->>Sys: 开始音视频捕获

    Note over App,Sys: 发布媒体流

    Sys-->>WTC: 音视频数据
    WTC-->>iOS: RTCVideoFrame/RTCAudioBuffer
    iOS-->>MC: 通过 EventChannel 传递
    MC-->>FW: onTrack event
    FW-->>LK: Track events
    LK-->>App: UI 更新
```

## 6. 关键组件说明

### 6.1 依赖配置
```yaml
# pubspec.yaml
flutter_webrtc: ^0.14.1  # Dart 层依赖
```

```ruby
# ios/livekit_client.podspec
s.dependency 'WebRTC-SDK', '125.6422.07'  # 底层 WebRTC
s.dependency 'flutter_webrtc'             # Flutter 桥接
```

### 6.2 核心文件结构
```
client-sdk-flutter/
├── lib/src/                    # Dart SDK 实现
│   ├── core/room.dart         # 房间管理
│   ├── core/engine.dart       # WebRTC 引擎
│   └── widgets/               # Flutter UI 组件
├── ios/                       # iOS 插件配置
│   └── livekit_client.podspec # CocoaPods 配置
└── shared_swift/              # iOS 原生实现
    └── LiveKitPlugin.swift    # 原生功能扩展
```

### 6.3 通信机制
1. **Method Channel**: Dart → iOS 方法调用
2. **Event Channel**: iOS → Dart 事件通知
3. **Platform View**: 原生 UI 组件嵌入

## 7. 总结

### 7.1 依赖关系总结

| 组件 | 作用 | 层级 |
|------|------|------|
| **LiveKit SDK** | 高级 API，房间管理、参与者管理 | 应用层 |
| **flutter_webrtc** | WebRTC Dart API 封装 | 抽象层 |
| **WebRTC-SDK** | iOS 原生 WebRTC 实现 | 原生层 |

### 7.2 为什么需要双重依赖

LiveKit iOS 插件同时依赖这两个库是因为：

1. **flutter_webrtc** 提供 **Flutter 集成能力**
   - 与 Flutter 层协调
   - 访问已创建的对象
   - 复用标准封装

2. **WebRTC-SDK** 提供 **原生扩展能力**
   - 直接访问 WebRTC 原生 API
   - 实现高级音频处理功能
   - 配置 iOS 音频会话

3. **两者互补**，缺一不可

### 7.3 架构优势

这种架构实现了：
- **跨平台统一**: 通过 Dart API 提供一致的开发体验
- **原生性能**: 底层使用 Google WebRTC 确保音视频质量
- **Flutter 集成**: 无缝集成到 Flutter 应用中
- **功能扩展**: LiveKit 在标准 WebRTC 基础上提供房间、参与者等高级概念

每一层都有明确的职责，通过标准的 Flutter 插件机制实现跨语言通信，确保了系统的可维护性和扩展性。
