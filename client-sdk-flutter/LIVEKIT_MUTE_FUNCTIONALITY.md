# LiveKit Flutter SDK - 麦克风静音功能详解

## 概述

LiveKit Flutter SDK支持两种麦克风静音模式：真静音和假静音。通过`stopAudioCaptureOnMute`参数控制。

## 两种静音模式

### 1. 真静音模式（默认）
```dart
// stopAudioCaptureOnMute = true (默认值)
final audioOptions = AudioCaptureOptions(
  stopAudioCaptureOnMute: true,
);
```

**行为特点：**
- 完全停止麦克风音频采集
- 释放音频采集资源
- 可能触发Audio Session重新配置
- 无法进行本地音频处理

### 2. 假静音模式
```dart
// stopAudioCaptureOnMute = false
final audioOptions = AudioCaptureOptions(
  stopAudioCaptureOnMute: false,
);
```

**行为特点：**
- 麦克风硬件继续工作，持续采集音频数据
- 不向服务器发送音频数据（其他参与者听不到）
- 保持音频采集资源活跃
- 可以继续进行本地音频处理（如音频可视化）
- 不会触发Audio Session重新配置

## 平台差异

### 自动启用假静音的平台
- **Windows**: 自动跳过完全停止音频采集
- **Web Firefox**: 自动跳过完全停止音频采集

### 需要手动配置的平台
- **iOS**: 需要明确设置`stopAudioCaptureOnMute = false`
- **Android**: 需要明确设置`stopAudioCaptureOnMute = false`
- **macOS**: 需要明确设置`stopAudioCaptureOnMute = false`
- **Linux**: 需要明确设置`stopAudioCaptureOnMute = false`

## 使用示例

```dart
// 设置假静音模式
final audioOptions = AudioCaptureOptions(
  stopAudioCaptureOnMute: false,
);

// 启用麦克风
await localParticipant.setMicrophoneEnabled(true, 
    audioCaptureOptions: audioOptions);

// 静音（继续采集，停止发送）
await localParticipant.setMicrophoneEnabled(false);

// 取消静音
await localParticipant.setMicrophoneEnabled(true);
```

## Audio Session影响

### stopAudioCaptureOnMute = true
- **mute()**: 调用`mediaStreamTrack.stop()`
- **unmute()**: 调用`restartTrack()`重新创建track
- **Audio Session**: 可能切换session配置

### stopAudioCaptureOnMute = false
- **mute()**: 仅设置`mediaStreamTrack.enabled = false`
- **unmute()**: 仅设置`mediaStreamTrack.enabled = true`
- **Audio Session**: 不会切换session配置

## 相关文件位置

- **静音实现**: `lib/src/track/local/local.dart:109-132`
- **配置选项**: `lib/src/track/options.dart:283-284`
- **平台检测**: `lib/src/support/platform.dart:43-46`
- **Audio Session管理**: `lib/src/track/audio_management.dart`
- **高级API**: `lib/src/participant/local.dart:687-692`

## 建议

- 如需本地音频处理或避免Audio Session切换，使用`stopAudioCaptureOnMute = false`
- 如需完全释放音频资源，使用`stopAudioCaptureOnMute = true`（默认）
- iOS平台需要明确配置才能实现假静音功能