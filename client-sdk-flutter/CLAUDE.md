# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **LiveKit Flutter Client SDK** - a comprehensive WebRTC-based real-time communication SDK that enables video conferencing, audio streaming, and data transmission across Flutter applications. The SDK supports all major platforms (iOS, Android, Web, macOS, Windows, Linux).

## Development Commands

### Core Flutter Commands
- `flutter pub get` - Install dependencies
- `flutter analyze` - Run static analysis (uses lints/recommended.yaml)
- `flutter test` - Run all unit tests
- `flutter test test/core/room_e2e_test.dart` - Run specific E2E test (requires LiveKit server)
- `flutter test test/utils_test.dart` - Run specific unit test
- `flutter format .` - Format code
- `dart run import_sorter:main` - Sort imports (configured in pubspec.yaml)

### Build Commands
- `flutter build apk` - Build Android APK
- `flutter build ios` - Build iOS app
- `flutter build web` - Build web version
- `flutter build macos` - Build macOS app
- `flutter build windows` - Build Windows app
- `flutter build linux` - Build Linux app

### Example App
- `cd example && flutter run` - Run the example conferencing app
- `cd example && flutter run -d web-server` - Run example in web browser
- `cd example && flutter test` - Run example app tests
- `cd example && flutter build web && flutter run -d web-server` - Build and run web version

### SDK-Specific Commands
- `make proto` - Generate protobuf files (requires ../protocol/protobufs directory)
- `make format` - Format generated protobuf files
- `make e2ee` - Compile E2EE worker for web platform
- `./bootstrap.sh` - Setup protobuf compiler plugin

## Architecture Overview

### Core Components

**Room & Engine (`lib/src/core/`)**
- `Room` - Main entry point for LiveKit sessions, manages participants and tracks
- `Engine` - WebRTC engine handling peer connections and media transport
- `SignalClient` - WebSocket-based signaling for room state synchronization
- `Transport` - Low-level WebRTC transport layer

**Participants (`lib/src/participant/`)**
- `LocalParticipant` - Represents the current user, manages publishing tracks
- `RemoteParticipant` - Represents other users in the room
- `Participant` - Base class with common functionality

**Tracks (`lib/src/track/`)**
- **Local tracks**: `LocalAudioTrack`, `LocalVideoTrack` for publishing media
- **Remote tracks**: `RemoteAudioTrack`, `RemoteVideoTrack` for receiving media
- **Audio processing**: Includes FFT-based audio visualization and WebRTC audio management
- **Video rendering**: `VideoTrackRenderer` widget for displaying video streams

**Platform Abstraction**
- **Native platforms** (iOS/Android/macOS/Windows): Swift/Kotlin native plugins with audio processing
- **Web platform**: Dart-based implementation using WebRTC APIs and Web Workers for E2EE

### Key Features

**End-to-End Encryption (E2EE)**
- `E2EEManager` handles encryption/decryption
- Web platform requires compiled `e2ee.worker.dart.js` worker
- Native platforms have built-in E2EE support

**Audio Visualization**
- FFT-based audio analysis in native code (`FFTProcessor.swift`, `FFTAudioAnalyzer.kt`)
- Cross-platform `AudioVisualizer` with platform-specific implementations
- Real-time waveform data for UI components

**Multi-platform Support**
- Shared Swift code (`shared_swift/`) for iOS/macOS
- Platform-specific plugins for each target
- Web-specific implementations in `web/` directory

## Code Organization

```
lib/src/
├── core/           # Room management and WebRTC engine
├── participant/    # Local and remote participant handling  
├── track/         # Audio/video track management and processing
├── publication/   # Track publication abstractions
├── events.dart    # Event system for state changes
├── e2ee/         # End-to-end encryption
├── proto/        # Generated protobuf files (LiveKit protocol)
├── support/      # Platform abstraction and utilities
├── hardware/     # Device hardware management
└── widgets/      # Flutter UI components

example/          # Full-featured conferencing app
test/            # Unit and integration tests  
{platform}/      # Platform-specific native code
```

## Development Notes

### Protocol Buffers
- Generated files in `lib/src/proto/` should not be edited manually
- Run `make proto` after protocol updates (requires livekit/protocol checkout)
- Protobuf files are excluded from linting via `analysis_options.yaml`

### Testing
- E2E tests in `test/core/room_e2e_test.dart` require LiveKit server
- Mock implementations available in `test/mock/` for unit testing WebRTC components
- Core functionality tests: `test/core/` (data streams, RPC, signal client)
- Platform-specific testing may require physical devices for camera/microphone features
- Use `flutter test --coverage` for test coverage reports

### Platform Considerations
- **iOS**: Minimum deployment target 12.1, requires camera/microphone permissions
- **Android**: Requires multiple permissions for media and network access
- **Web**: E2EE requires pre-compiled worker, may need HTTPS for camera access
- **Desktop**: Screen sharing uses native capture APIs

### Event-Driven Architecture
The SDK uses both `ChangeNotifier` pattern and typed `EventsListener<T>` for reactive UI development. Key events include participant join/leave, track publish/unpublish, and connection state changes.