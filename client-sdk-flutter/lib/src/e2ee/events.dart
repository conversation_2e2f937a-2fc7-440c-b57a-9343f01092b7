// Copyright 2024 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import '../../livekit_client.dart';

enum E2EEState {
  kNew,
  kOk,
  kKeyRatcheted,
  kMissingKey,
  kEncryptionFailed,
  kDecryptionFailed,
  kInternalError,
}

/// The [E2EEState] on the track.
/// Emitted by [E2EEManager].
class TrackE2EEStateEvent with RoomEvent, ParticipantEvent {
  final Participant participant;
  final TrackPublication publication;
  final E2EEState state;
  const TrackE2EEStateEvent({
    required this.participant,
    required this.publication,
    required this.state,
  });

  @override
  String toString() => '${runtimeType}'
      '(participant: ${participant}, publication: ${publication}, state: ${state})';
}
