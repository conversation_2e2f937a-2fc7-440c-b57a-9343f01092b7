# Copyright 2024 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: livekit_client
description: Flutter Client SDK for LiveKit.
  Build real-time video and audio into your apps. Supports iOS, Android, and Web.
version: 2.4.8
homepage: https://github.com/livekit/client-sdk-flutter

environment:
  sdk: ">=3.6.0 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter_web_plugins:
    sdk: flutter
  flutter:
    sdk: flutter
  async: ^2.9.0
  collection: ^1.19.0
  connectivity_plus: ^6.0.2
  fixnum: ^1.0.1
  meta: ^1.8.0
  http: ^1.3.0
  logging: ^1.1.0
  uuid: ^4.5.1
  synchronized: ^3.0.0+3
  protobuf: ">=3.0.0"
  flutter_webrtc: ^0.14.1
  device_info_plus: ^11.3.0
  dart_webrtc: ^1.5.3
  sdp_transform: ^0.3.2
  web: ^1.0.0
  mime_type: ^1.0.1
  path: ^1.9.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  lints: ^4.0.0
  mockito: ^5.3.2
  import_sorter: ^4.6.0
  yaml: ^3.1.2

import_sorter:
  comments: false
  ignored_files: # Ignore proto files
    - \/lib\/src\/proto\/*

flutter:
  plugin:
    platforms:
      ios:
        pluginClass: LiveKitPlugin
      android:
        package: io.livekit.plugin
        pluginClass: LiveKitPlugin
      macos:
        pluginClass: LiveKitPlugin
      windows:
        pluginClass: LiveKitPlugin
      web:
        pluginClass: LiveKitWebPlugin
        fileName: livekit_client_web.dart


topics:
  - webrtc
  - voip
  - video
  - livestream
  - conference
  
