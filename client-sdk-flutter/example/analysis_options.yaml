# Copyright 2024 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.

# https://pub.dev/packages/flutter_lints

include: package:flutter_lints/flutter.yaml

analyzer:
  #
  # Enforce stricter type-checking
  # https://dart.dev/guides/language/analysis-options#enabling-additional-type-checks
  # https://dash-overflow.net/articles/getting_started/#step-3-disabling-_implicit-dynamic_--_implicit-cast_
  #

  #
  # exclude protobuf files
  #
  exclude:
    - '**/*.pb.dart'
    - '**/*.pbenum.dart'
    - '**/*.pbjson.dart'
    - '**/*.pbserver.dart'

linter:
  rules:
    #
    # Additional recommended rules
    #
    prefer_single_quotes: true
    unnecessary_brace_in_string_interps: false
    unawaited_futures: true

    #
    # Turn off avoid_print for example projects
    #
    avoid_print: false
    depend_on_referenced_packages: false
    use_build_context_synchronously: false
