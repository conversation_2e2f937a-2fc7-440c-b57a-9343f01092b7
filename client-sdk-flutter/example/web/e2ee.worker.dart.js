// Generated by dart2js (NullSafetyMode.sound, csp, intern-composite-values), the Dart to JavaScript compiler version: 3.7.0.
// The code supports the following hooks:
// dartPrint(message):
//    if this function is defined it is called instead of the Dart [print]
//    method.
//
// dartMainRunner(main, args):
//    if this function is defined, the Dart [main] method will not be invoked
//    directly. Instead, a closure that will invoke [main], and its arguments
//    [args] is passed to [dart<PERSON>ain<PERSON><PERSON><PERSON>].
//
// dartDeferredLibraryLoader(uri, successCallback, errorCallback, loadId, loadPriority):
//    if this function is defined, it will be called when a deferred library
//    is loaded. It should load and eval the javascript of `uri`, and call
//    successCallback. If it fails to do so, it should call errorCallback with
//    an error. The loadId argument is the deferred import that resulted in
//    this uri being loaded. The loadPriority argument is an arbitrary argument
//    string forwarded from the 'dart2js:load-priority' pragma option.
// dartDeferredLibraryMultiLoader(uris, successCallback, errorCallback, loadId, loadPriority):
//    if this function is defined, it will be called when a deferred library
//    is loaded. It should load and eval the javascript of every URI in `uris`,
//    and call successCallback. If it fails to do so, it should call
//    errorCallback with an error. The loadId argument is the deferred import
//    that resulted in this uri being loaded. The loadPriority argument is an
//    arbitrary argument string forwarded from the 'dart2js:load-priority'
//    pragma option.
//
// dartCallInstrumentation(id, qualifiedName):
//    if this function is defined, it will be called at each entry of a
//    method or constructor. Used only when compiling programs with
//    --experiment-call-instrumentation.
(function dartProgram() {
  function copyProperties(from, to) {
    var keys = Object.keys(from);
    for (var i = 0; i < keys.length; i++) {
      var key = keys[i];
      to[key] = from[key];
    }
  }
  function mixinPropertiesHard(from, to) {
    var keys = Object.keys(from);
    for (var i = 0; i < keys.length; i++) {
      var key = keys[i];
      if (!to.hasOwnProperty(key)) {
        to[key] = from[key];
      }
    }
  }
  function mixinPropertiesEasy(from, to) {
    Object.assign(to, from);
  }
  var supportsDirectProtoAccess = function() {
    var cls = function() {
    };
    cls.prototype = {p: {}};
    var object = new cls();
    if (!(Object.getPrototypeOf(object) && Object.getPrototypeOf(object).p === cls.prototype.p))
      return false;
    try {
      if (typeof navigator != "undefined" && typeof navigator.userAgent == "string" && navigator.userAgent.indexOf("Chrome/") >= 0)
        return true;
      if (typeof version == "function" && version.length == 0) {
        var v = version();
        if (/^\d+\.\d+\.\d+\.\d+$/.test(v))
          return true;
      }
    } catch (_) {
    }
    return false;
  }();
  function inherit(cls, sup) {
    cls.prototype.constructor = cls;
    cls.prototype["$is" + cls.name] = cls;
    if (sup != null) {
      if (supportsDirectProtoAccess) {
        Object.setPrototypeOf(cls.prototype, sup.prototype);
        return;
      }
      var clsPrototype = Object.create(sup.prototype);
      copyProperties(cls.prototype, clsPrototype);
      cls.prototype = clsPrototype;
    }
  }
  function inheritMany(sup, classes) {
    for (var i = 0; i < classes.length; i++) {
      inherit(classes[i], sup);
    }
  }
  function mixinEasy(cls, mixin) {
    mixinPropertiesEasy(mixin.prototype, cls.prototype);
    cls.prototype.constructor = cls;
  }
  function mixinHard(cls, mixin) {
    mixinPropertiesHard(mixin.prototype, cls.prototype);
    cls.prototype.constructor = cls;
  }
  function lazy(holder, name, getterName, initializer) {
    var uninitializedSentinel = holder;
    holder[name] = uninitializedSentinel;
    holder[getterName] = function() {
      if (holder[name] === uninitializedSentinel) {
        holder[name] = initializer();
      }
      holder[getterName] = function() {
        return this[name];
      };
      return holder[name];
    };
  }
  function lazyFinal(holder, name, getterName, initializer) {
    var uninitializedSentinel = holder;
    holder[name] = uninitializedSentinel;
    holder[getterName] = function() {
      if (holder[name] === uninitializedSentinel) {
        var value = initializer();
        if (holder[name] !== uninitializedSentinel) {
          A.throwLateFieldADI(name);
        }
        holder[name] = value;
      }
      var finalValue = holder[name];
      holder[getterName] = function() {
        return finalValue;
      };
      return finalValue;
    };
  }
  function makeConstList(list) {
    list.$flags = 7;
    return list;
  }
  function convertToFastObject(properties) {
    function t() {
    }
    t.prototype = properties;
    new t();
    return properties;
  }
  function convertAllToFastObject(arrayOfObjects) {
    for (var i = 0; i < arrayOfObjects.length; ++i) {
      convertToFastObject(arrayOfObjects[i]);
    }
  }
  var functionCounter = 0;
  function instanceTearOffGetter(isIntercepted, parameters) {
    var cache = null;
    return isIntercepted ? function(receiver) {
      if (cache === null)
        cache = A.closureFromTearOff(parameters);
      return new cache(receiver, this);
    } : function() {
      if (cache === null)
        cache = A.closureFromTearOff(parameters);
      return new cache(this, null);
    };
  }
  function staticTearOffGetter(parameters) {
    var cache = null;
    return function() {
      if (cache === null)
        cache = A.closureFromTearOff(parameters).prototype;
      return cache;
    };
  }
  var typesOffset = 0;
  function tearOffParameters(container, isStatic, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, needsDirectAccess) {
    if (typeof funType == "number") {
      funType += typesOffset;
    }
    return {co: container, iS: isStatic, iI: isIntercepted, rC: requiredParameterCount, dV: optionalParameterDefaultValues, cs: callNames, fs: funsOrNames, fT: funType, aI: applyIndex || 0, nDA: needsDirectAccess};
  }
  function installStaticTearOff(holder, getterName, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex) {
    var parameters = tearOffParameters(holder, true, false, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, false);
    var getterFunction = staticTearOffGetter(parameters);
    holder[getterName] = getterFunction;
  }
  function installInstanceTearOff(prototype, getterName, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, needsDirectAccess) {
    isIntercepted = !!isIntercepted;
    var parameters = tearOffParameters(prototype, false, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, !!needsDirectAccess);
    var getterFunction = instanceTearOffGetter(isIntercepted, parameters);
    prototype[getterName] = getterFunction;
  }
  function setOrUpdateInterceptorsByTag(newTags) {
    var tags = init.interceptorsByTag;
    if (!tags) {
      init.interceptorsByTag = newTags;
      return;
    }
    copyProperties(newTags, tags);
  }
  function setOrUpdateLeafTags(newTags) {
    var tags = init.leafTags;
    if (!tags) {
      init.leafTags = newTags;
      return;
    }
    copyProperties(newTags, tags);
  }
  function updateTypes(newTypes) {
    var types = init.types;
    var length = types.length;
    types.push.apply(types, newTypes);
    return length;
  }
  function updateHolder(holder, newHolder) {
    copyProperties(newHolder, holder);
    return holder;
  }
  var hunkHelpers = function() {
    var mkInstance = function(isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, applyIndex) {
        return function(container, getterName, name, funType) {
          return installInstanceTearOff(container, getterName, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, [name], funType, applyIndex, false);
        };
      },
      mkStatic = function(requiredParameterCount, optionalParameterDefaultValues, callNames, applyIndex) {
        return function(container, getterName, name, funType) {
          return installStaticTearOff(container, getterName, requiredParameterCount, optionalParameterDefaultValues, callNames, [name], funType, applyIndex);
        };
      };
    return {inherit: inherit, inheritMany: inheritMany, mixin: mixinEasy, mixinHard: mixinHard, installStaticTearOff: installStaticTearOff, installInstanceTearOff: installInstanceTearOff, _instance_0u: mkInstance(0, 0, null, ["call$0"], 0), _instance_1u: mkInstance(0, 1, null, ["call$1"], 0), _instance_2u: mkInstance(0, 2, null, ["call$2"], 0), _instance_0i: mkInstance(1, 0, null, ["call$0"], 0), _instance_1i: mkInstance(1, 1, null, ["call$1"], 0), _instance_2i: mkInstance(1, 2, null, ["call$2"], 0), _static_0: mkStatic(0, null, ["call$0"], 0), _static_1: mkStatic(1, null, ["call$1"], 0), _static_2: mkStatic(2, null, ["call$2"], 0), makeConstList: makeConstList, lazy: lazy, lazyFinal: lazyFinal, updateHolder: updateHolder, convertToFastObject: convertToFastObject, updateTypes: updateTypes, setOrUpdateInterceptorsByTag: setOrUpdateInterceptorsByTag, setOrUpdateLeafTags: setOrUpdateLeafTags};
  }();
  function initializeDeferredHunk(hunk) {
    typesOffset = init.types.length;
    hunk(hunkHelpers, init, holders, $);
  }
  var J = {
    makeDispatchRecord(interceptor, proto, extension, indexability) {
      return {i: interceptor, p: proto, e: extension, x: indexability};
    },
    getNativeInterceptor(object) {
      var proto, objectProto, $constructor, interceptor, t1,
        record = object[init.dispatchPropertyName];
      if (record == null)
        if ($.initNativeDispatchFlag == null) {
          A.initNativeDispatch();
          record = object[init.dispatchPropertyName];
        }
      if (record != null) {
        proto = record.p;
        if (false === proto)
          return record.i;
        if (true === proto)
          return object;
        objectProto = Object.getPrototypeOf(object);
        if (proto === objectProto)
          return record.i;
        if (record.e === objectProto)
          throw A.wrapException(A.UnimplementedError$("Return interceptor for " + A.S(proto(object, record))));
      }
      $constructor = object.constructor;
      if ($constructor == null)
        interceptor = null;
      else {
        t1 = $._JS_INTEROP_INTERCEPTOR_TAG;
        if (t1 == null)
          t1 = $._JS_INTEROP_INTERCEPTOR_TAG = init.getIsolateTag("_$dart_js");
        interceptor = $constructor[t1];
      }
      if (interceptor != null)
        return interceptor;
      interceptor = A.lookupAndCacheInterceptor(object);
      if (interceptor != null)
        return interceptor;
      if (typeof object == "function")
        return B.JavaScriptFunction_methods;
      proto = Object.getPrototypeOf(object);
      if (proto == null)
        return B.PlainJavaScriptObject_methods;
      if (proto === Object.prototype)
        return B.PlainJavaScriptObject_methods;
      if (typeof $constructor == "function") {
        t1 = $._JS_INTEROP_INTERCEPTOR_TAG;
        if (t1 == null)
          t1 = $._JS_INTEROP_INTERCEPTOR_TAG = init.getIsolateTag("_$dart_js");
        Object.defineProperty($constructor, t1, {value: B.UnknownJavaScriptObject_methods, enumerable: false, writable: true, configurable: true});
        return B.UnknownJavaScriptObject_methods;
      }
      return B.UnknownJavaScriptObject_methods;
    },
    JSArray_JSArray$fixed($length, $E) {
      if ($length < 0 || $length > 4294967295)
        throw A.wrapException(A.RangeError$range($length, 0, 4294967295, "length", null));
      return J.JSArray_JSArray$markFixed(new Array($length), $E);
    },
    JSArray_JSArray$markFixed(allocation, $E) {
      var t1 = A._setArrayType(allocation, $E._eval$1("JSArray<0>"));
      t1.$flags = 1;
      return t1;
    },
    getInterceptor$(receiver) {
      if (typeof receiver == "number") {
        if (Math.floor(receiver) == receiver)
          return J.JSInt.prototype;
        return J.JSNumNotInt.prototype;
      }
      if (typeof receiver == "string")
        return J.JSString.prototype;
      if (receiver == null)
        return J.JSNull.prototype;
      if (typeof receiver == "boolean")
        return J.JSBool.prototype;
      if (Array.isArray(receiver))
        return J.JSArray.prototype;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        if (typeof receiver == "symbol")
          return J.JavaScriptSymbol.prototype;
        if (typeof receiver == "bigint")
          return J.JavaScriptBigInt.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    getInterceptor$asx(receiver) {
      if (typeof receiver == "string")
        return J.JSString.prototype;
      if (receiver == null)
        return receiver;
      if (Array.isArray(receiver))
        return J.JSArray.prototype;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        if (typeof receiver == "symbol")
          return J.JavaScriptSymbol.prototype;
        if (typeof receiver == "bigint")
          return J.JavaScriptBigInt.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    getInterceptor$ax(receiver) {
      if (receiver == null)
        return receiver;
      if (Array.isArray(receiver))
        return J.JSArray.prototype;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        if (typeof receiver == "symbol")
          return J.JavaScriptSymbol.prototype;
        if (typeof receiver == "bigint")
          return J.JavaScriptBigInt.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    getInterceptor$x(receiver) {
      if (receiver == null)
        return receiver;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        if (typeof receiver == "symbol")
          return J.JavaScriptSymbol.prototype;
        if (typeof receiver == "bigint")
          return J.JavaScriptBigInt.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    get$buffer$x(receiver) {
      return J.getInterceptor$x(receiver).get$buffer(receiver);
    },
    get$hashCode$(receiver) {
      return J.getInterceptor$(receiver).get$hashCode(receiver);
    },
    get$iterator$ax(receiver) {
      return J.getInterceptor$ax(receiver).get$iterator(receiver);
    },
    get$length$asx(receiver) {
      return J.getInterceptor$asx(receiver).get$length(receiver);
    },
    get$runtimeType$(receiver) {
      return J.getInterceptor$(receiver).get$runtimeType(receiver);
    },
    $eq$(receiver, a0) {
      if (receiver == null)
        return a0 == null;
      if (typeof receiver != "object")
        return a0 != null && receiver === a0;
      return J.getInterceptor$(receiver).$eq(receiver, a0);
    },
    $index$asx(receiver, a0) {
      if (typeof a0 === "number")
        if (Array.isArray(receiver) || typeof receiver == "string" || A.isJsIndexable(receiver, receiver[init.dispatchPropertyName]))
          if (a0 >>> 0 === a0 && a0 < receiver.length)
            return receiver[a0];
      return J.getInterceptor$asx(receiver).$index(receiver, a0);
    },
    _setInt8$2$x(receiver, a0, a1) {
      return J.getInterceptor$x(receiver)._setInt8$2(receiver, a0, a1);
    },
    add$1$ax(receiver, a0) {
      return J.getInterceptor$ax(receiver).add$1(receiver, a0);
    },
    asUint8List$0$x(receiver) {
      return J.getInterceptor$x(receiver).asUint8List$0(receiver);
    },
    asUint8List$2$x(receiver, a0, a1) {
      return J.getInterceptor$x(receiver).asUint8List$2(receiver, a0, a1);
    },
    elementAt$1$ax(receiver, a0) {
      return J.getInterceptor$ax(receiver).elementAt$1(receiver, a0);
    },
    map$1$1$ax(receiver, a0, $T1) {
      return J.getInterceptor$ax(receiver).map$1$1(receiver, a0, $T1);
    },
    noSuchMethod$1$(receiver, a0) {
      return J.getInterceptor$(receiver).noSuchMethod$1(receiver, a0);
    },
    toString$0$(receiver) {
      return J.getInterceptor$(receiver).toString$0(receiver);
    },
    Interceptor: function Interceptor() {
    },
    JSBool: function JSBool() {
    },
    JSNull: function JSNull() {
    },
    JavaScriptObject: function JavaScriptObject() {
    },
    LegacyJavaScriptObject: function LegacyJavaScriptObject() {
    },
    PlainJavaScriptObject: function PlainJavaScriptObject() {
    },
    UnknownJavaScriptObject: function UnknownJavaScriptObject() {
    },
    JavaScriptFunction: function JavaScriptFunction() {
    },
    JavaScriptBigInt: function JavaScriptBigInt() {
    },
    JavaScriptSymbol: function JavaScriptSymbol() {
    },
    JSArray: function JSArray(t0) {
      this.$ti = t0;
    },
    JSUnmodifiableArray: function JSUnmodifiableArray(t0) {
      this.$ti = t0;
    },
    ArrayIterator: function ArrayIterator(t0, t1, t2) {
      var _ = this;
      _._iterable = t0;
      _._length = t1;
      _._index = 0;
      _._current = null;
      _.$ti = t2;
    },
    JSNumber: function JSNumber() {
    },
    JSInt: function JSInt() {
    },
    JSNumNotInt: function JSNumNotInt() {
    },
    JSString: function JSString() {
    }
  },
  A = {JS_CONST: function JS_CONST() {
    },
    SystemHash_combine(hash, value) {
      hash = hash + value & 536870911;
      hash = hash + ((hash & 524287) << 10) & 536870911;
      return hash ^ hash >>> 6;
    },
    SystemHash_finish(hash) {
      hash = hash + ((hash & 67108863) << 3) & 536870911;
      hash ^= hash >>> 11;
      return hash + ((hash & 16383) << 15) & 536870911;
    },
    checkNotNullable(value, $name, $T) {
      return value;
    },
    isToStringVisiting(object) {
      var t1, i;
      for (t1 = $.toStringVisiting.length, i = 0; i < t1; ++i)
        if (object === $.toStringVisiting[i])
          return true;
      return false;
    },
    MappedIterable_MappedIterable(iterable, $function, $S, $T) {
      if (type$.EfficientLengthIterable_dynamic._is(iterable))
        return new A.EfficientLengthMappedIterable(iterable, $function, $S._eval$1("@<0>")._bind$1($T)._eval$1("EfficientLengthMappedIterable<1,2>"));
      return new A.MappedIterable(iterable, $function, $S._eval$1("@<0>")._bind$1($T)._eval$1("MappedIterable<1,2>"));
    },
    _CopyingBytesBuilder: function _CopyingBytesBuilder(t0) {
      this.__internal$_length = 0;
      this._buffer = t0;
    },
    LateError: function LateError(t0) {
      this._message = t0;
    },
    SentinelValue: function SentinelValue() {
    },
    EfficientLengthIterable: function EfficientLengthIterable() {
    },
    ListIterable: function ListIterable() {
    },
    ListIterator: function ListIterator(t0, t1, t2) {
      var _ = this;
      _.__internal$_iterable = t0;
      _.__internal$_length = t1;
      _.__internal$_index = 0;
      _.__internal$_current = null;
      _.$ti = t2;
    },
    MappedIterable: function MappedIterable(t0, t1, t2) {
      this.__internal$_iterable = t0;
      this._f = t1;
      this.$ti = t2;
    },
    EfficientLengthMappedIterable: function EfficientLengthMappedIterable(t0, t1, t2) {
      this.__internal$_iterable = t0;
      this._f = t1;
      this.$ti = t2;
    },
    MappedIterator: function MappedIterator(t0, t1, t2) {
      var _ = this;
      _.__internal$_current = null;
      _._iterator = t0;
      _._f = t1;
      _.$ti = t2;
    },
    MappedListIterable: function MappedListIterable(t0, t1, t2) {
      this._source = t0;
      this._f = t1;
      this.$ti = t2;
    },
    WhereIterable: function WhereIterable(t0, t1, t2) {
      this.__internal$_iterable = t0;
      this._f = t1;
      this.$ti = t2;
    },
    WhereIterator: function WhereIterator(t0, t1, t2) {
      this._iterator = t0;
      this._f = t1;
      this.$ti = t2;
    },
    FixedLengthListMixin: function FixedLengthListMixin() {
    },
    Symbol: function Symbol(t0) {
      this.__internal$_name = t0;
    },
    unminifyOrTag(rawClassName) {
      var preserved = init.mangledGlobalNames[rawClassName];
      if (preserved != null)
        return preserved;
      return rawClassName;
    },
    isJsIndexable(object, record) {
      var result;
      if (record != null) {
        result = record.x;
        if (result != null)
          return result;
      }
      return type$.JavaScriptIndexingBehavior_dynamic._is(object);
    },
    S(value) {
      var result;
      if (typeof value == "string")
        return value;
      if (typeof value == "number") {
        if (value !== 0)
          return "" + value;
      } else if (true === value)
        return "true";
      else if (false === value)
        return "false";
      else if (value == null)
        return "null";
      result = J.toString$0$(value);
      return result;
    },
    Primitives_objectHashCode(object) {
      var hash,
        property = $.Primitives__identityHashCodeProperty;
      if (property == null)
        property = $.Primitives__identityHashCodeProperty = Symbol("identityHashCode");
      hash = object[property];
      if (hash == null) {
        hash = Math.random() * 0x3fffffff | 0;
        object[property] = hash;
      }
      return hash;
    },
    Primitives_objectTypeName(object) {
      return A.Primitives__objectTypeNameNewRti(object);
    },
    Primitives__objectTypeNameNewRti(object) {
      var interceptor, dispatchName, $constructor, constructorName;
      if (object instanceof A.Object)
        return A._rtiToString(A.instanceType(object), null);
      interceptor = J.getInterceptor$(object);
      if (interceptor === B.Interceptor_methods || interceptor === B.JavaScriptObject_methods || type$.UnknownJavaScriptObject._is(object)) {
        dispatchName = B.C_JS_CONST(object);
        if (dispatchName !== "Object" && dispatchName !== "")
          return dispatchName;
        $constructor = object.constructor;
        if (typeof $constructor == "function") {
          constructorName = $constructor.name;
          if (typeof constructorName == "string" && constructorName !== "Object" && constructorName !== "")
            return constructorName;
        }
      }
      return A._rtiToString(A.instanceType(object), null);
    },
    Primitives_safeToString(object) {
      if (typeof object == "number" || A._isBool(object))
        return J.toString$0$(object);
      if (typeof object == "string")
        return JSON.stringify(object);
      if (object instanceof A.Closure)
        return object.toString$0(0);
      return "Instance of '" + A.Primitives_objectTypeName(object) + "'";
    },
    Primitives_stringFromNativeUint8List(charCodes, start, end) {
      var i, result, i0, chunkEnd;
      if (end <= 500 && start === 0 && end === charCodes.length)
        return String.fromCharCode.apply(null, charCodes);
      for (i = start, result = ""; i < end; i = i0) {
        i0 = i + 500;
        chunkEnd = i0 < end ? i0 : end;
        result += String.fromCharCode.apply(null, charCodes.subarray(i, chunkEnd));
      }
      return result;
    },
    Primitives_lazyAsJsDate(receiver) {
      if (receiver.date === void 0)
        receiver.date = new Date(receiver._value);
      return receiver.date;
    },
    Primitives_getYear(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCFullYear() + 0 : A.Primitives_lazyAsJsDate(receiver).getFullYear() + 0;
    },
    Primitives_getMonth(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCMonth() + 1 : A.Primitives_lazyAsJsDate(receiver).getMonth() + 1;
    },
    Primitives_getDay(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCDate() + 0 : A.Primitives_lazyAsJsDate(receiver).getDate() + 0;
    },
    Primitives_getHours(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCHours() + 0 : A.Primitives_lazyAsJsDate(receiver).getHours() + 0;
    },
    Primitives_getMinutes(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCMinutes() + 0 : A.Primitives_lazyAsJsDate(receiver).getMinutes() + 0;
    },
    Primitives_getSeconds(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCSeconds() + 0 : A.Primitives_lazyAsJsDate(receiver).getSeconds() + 0;
    },
    Primitives_getMilliseconds(receiver) {
      return receiver.isUtc ? A.Primitives_lazyAsJsDate(receiver).getUTCMilliseconds() + 0 : A.Primitives_lazyAsJsDate(receiver).getMilliseconds() + 0;
    },
    Primitives_functionNoSuchMethod($function, positionalArguments, namedArguments) {
      var $arguments, namedArgumentList, t1 = {};
      t1.argumentCount = 0;
      $arguments = [];
      namedArgumentList = [];
      t1.argumentCount = positionalArguments.length;
      B.JSArray_methods.addAll$1($arguments, positionalArguments);
      t1.names = "";
      if (namedArguments != null && namedArguments.__js_helper$_length !== 0)
        namedArguments.forEach$1(0, new A.Primitives_functionNoSuchMethod_closure(t1, namedArgumentList, $arguments));
      return J.noSuchMethod$1$($function, new A.JSInvocationMirror(B.Symbol_call, 0, $arguments, namedArgumentList, 0));
    },
    Primitives_applyFunction($function, positionalArguments, namedArguments) {
      var t1, argumentCount, jsStub;
      if (Array.isArray(positionalArguments))
        t1 = namedArguments == null || namedArguments.__js_helper$_length === 0;
      else
        t1 = false;
      if (t1) {
        argumentCount = positionalArguments.length;
        if (argumentCount === 0) {
          if (!!$function.call$0)
            return $function.call$0();
        } else if (argumentCount === 1) {
          if (!!$function.call$1)
            return $function.call$1(positionalArguments[0]);
        } else if (argumentCount === 2) {
          if (!!$function.call$2)
            return $function.call$2(positionalArguments[0], positionalArguments[1]);
        } else if (argumentCount === 3) {
          if (!!$function.call$3)
            return $function.call$3(positionalArguments[0], positionalArguments[1], positionalArguments[2]);
        } else if (argumentCount === 4) {
          if (!!$function.call$4)
            return $function.call$4(positionalArguments[0], positionalArguments[1], positionalArguments[2], positionalArguments[3]);
        } else if (argumentCount === 5)
          if (!!$function.call$5)
            return $function.call$5(positionalArguments[0], positionalArguments[1], positionalArguments[2], positionalArguments[3], positionalArguments[4]);
        jsStub = $function["call" + "$" + argumentCount];
        if (jsStub != null)
          return jsStub.apply($function, positionalArguments);
      }
      return A.Primitives__generalApplyFunction($function, positionalArguments, namedArguments);
    },
    Primitives__generalApplyFunction($function, positionalArguments, namedArguments) {
      var defaultValuesClosure, t1, defaultValues, interceptor, jsFunction, maxArguments, missingDefaults, keys, _i, defaultValue, used, key,
        $arguments = Array.isArray(positionalArguments) ? positionalArguments : A.List_List$of(positionalArguments, true, type$.dynamic),
        argumentCount = $arguments.length,
        requiredParameterCount = $function.$requiredArgCount;
      if (argumentCount < requiredParameterCount)
        return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
      defaultValuesClosure = $function.$defaultValues;
      t1 = defaultValuesClosure == null;
      defaultValues = !t1 ? defaultValuesClosure() : null;
      interceptor = J.getInterceptor$($function);
      jsFunction = interceptor["call*"];
      if (typeof jsFunction == "string")
        jsFunction = interceptor[jsFunction];
      if (t1) {
        if (namedArguments != null && namedArguments.__js_helper$_length !== 0)
          return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        if (argumentCount === requiredParameterCount)
          return jsFunction.apply($function, $arguments);
        return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
      }
      if (Array.isArray(defaultValues)) {
        if (namedArguments != null && namedArguments.__js_helper$_length !== 0)
          return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        maxArguments = requiredParameterCount + defaultValues.length;
        if (argumentCount > maxArguments)
          return A.Primitives_functionNoSuchMethod($function, $arguments, null);
        if (argumentCount < maxArguments) {
          missingDefaults = defaultValues.slice(argumentCount - requiredParameterCount);
          if ($arguments === positionalArguments)
            $arguments = A.List_List$of($arguments, true, type$.dynamic);
          B.JSArray_methods.addAll$1($arguments, missingDefaults);
        }
        return jsFunction.apply($function, $arguments);
      } else {
        if (argumentCount > requiredParameterCount)
          return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        if ($arguments === positionalArguments)
          $arguments = A.List_List$of($arguments, true, type$.dynamic);
        keys = Object.keys(defaultValues);
        if (namedArguments == null)
          for (t1 = keys.length, _i = 0; _i < keys.length; keys.length === t1 || (0, A.throwConcurrentModificationError)(keys), ++_i) {
            defaultValue = defaultValues[A._asString(keys[_i])];
            if (B.C__Required === defaultValue)
              return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
            B.JSArray_methods.add$1($arguments, defaultValue);
          }
        else {
          for (t1 = keys.length, used = 0, _i = 0; _i < keys.length; keys.length === t1 || (0, A.throwConcurrentModificationError)(keys), ++_i) {
            key = A._asString(keys[_i]);
            if (namedArguments.containsKey$1(key)) {
              ++used;
              B.JSArray_methods.add$1($arguments, namedArguments.$index(0, key));
            } else {
              defaultValue = defaultValues[key];
              if (B.C__Required === defaultValue)
                return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
              B.JSArray_methods.add$1($arguments, defaultValue);
            }
          }
          if (used !== namedArguments.__js_helper$_length)
            return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        }
        return jsFunction.apply($function, $arguments);
      }
    },
    Primitives_extractStackTrace(error) {
      var jsError = error.$thrownJsError;
      if (jsError == null)
        return null;
      return A.getTraceFromException(jsError);
    },
    Primitives_trySetStackTrace(error, stackTrace) {
      var jsError;
      if (error.$thrownJsError == null) {
        jsError = A.wrapException(error);
        error.$thrownJsError = jsError;
        jsError.stack = stackTrace.toString$0(0);
      }
    },
    iae(argument) {
      throw A.wrapException(A.argumentErrorValue(argument));
    },
    ioore(receiver, index) {
      if (receiver == null)
        J.get$length$asx(receiver);
      throw A.wrapException(A.diagnoseIndexError(receiver, index));
    },
    diagnoseIndexError(indexable, index) {
      var $length, _s5_ = "index";
      if (!A._isInt(index))
        return new A.ArgumentError(true, index, _s5_, null);
      $length = A._asInt(J.get$length$asx(indexable));
      if (index < 0 || index >= $length)
        return A.IndexError$withLength(index, $length, indexable, _s5_);
      return A.RangeError$value(index, _s5_);
    },
    diagnoseRangeError(start, end, $length) {
      if (start < 0 || start > $length)
        return A.RangeError$range(start, 0, $length, "start", null);
      if (end != null)
        if (end < start || end > $length)
          return A.RangeError$range(end, start, $length, "end", null);
      return new A.ArgumentError(true, end, "end", null);
    },
    argumentErrorValue(object) {
      return new A.ArgumentError(true, object, null, null);
    },
    wrapException(ex) {
      return A.initializeExceptionWrapper(new Error(), ex);
    },
    initializeExceptionWrapper(wrapper, ex) {
      var t1;
      if (ex == null)
        ex = new A.TypeError();
      wrapper.dartException = ex;
      t1 = A.toStringWrapper;
      if ("defineProperty" in Object) {
        Object.defineProperty(wrapper, "message", {get: t1});
        wrapper.name = "";
      } else
        wrapper.toString = t1;
      return wrapper;
    },
    toStringWrapper() {
      return J.toString$0$(this.dartException);
    },
    throwExpression(ex) {
      throw A.wrapException(ex);
    },
    throwExpressionWithWrapper(ex, wrapper) {
      throw A.initializeExceptionWrapper(wrapper, ex);
    },
    throwUnsupportedOperation(o, operation, verb) {
      var wrapper;
      if (operation == null)
        operation = 0;
      if (verb == null)
        verb = 0;
      wrapper = Error();
      A.throwExpressionWithWrapper(A._diagnoseUnsupportedOperation(o, operation, verb), wrapper);
    },
    _diagnoseUnsupportedOperation(o, encodedOperation, encodedVerb) {
      var operation, table, tableLength, index, verb, object, flags, article, adjective;
      if (typeof encodedOperation == "string")
        operation = encodedOperation;
      else {
        table = "[]=;add;removeWhere;retainWhere;removeRange;setRange;setInt8;setInt16;setInt32;setUint8;setUint16;setUint32;setFloat32;setFloat64".split(";");
        tableLength = table.length;
        index = encodedOperation;
        if (index > tableLength) {
          encodedVerb = index / tableLength | 0;
          index %= tableLength;
        }
        operation = table[index];
      }
      verb = typeof encodedVerb == "string" ? encodedVerb : "modify;remove from;add to".split(";")[encodedVerb];
      object = type$.List_dynamic._is(o) ? "list" : "ByteData";
      flags = o.$flags | 0;
      article = "a ";
      if ((flags & 4) !== 0)
        adjective = "constant ";
      else if ((flags & 2) !== 0) {
        adjective = "unmodifiable ";
        article = "an ";
      } else
        adjective = (flags & 1) !== 0 ? "fixed-length " : "";
      return new A.UnsupportedError("'" + operation + "': Cannot " + verb + " " + article + adjective + object);
    },
    throwConcurrentModificationError(collection) {
      throw A.wrapException(A.ConcurrentModificationError$(collection));
    },
    TypeErrorDecoder_extractPattern(message) {
      var match, $arguments, argumentsExpr, expr, method, receiver;
      message = A.quoteStringForRegExp(message.replace(String({}), "$receiver$"));
      match = message.match(/\\\$[a-zA-Z]+\\\$/g);
      if (match == null)
        match = A._setArrayType([], type$.JSArray_String);
      $arguments = match.indexOf("\\$arguments\\$");
      argumentsExpr = match.indexOf("\\$argumentsExpr\\$");
      expr = match.indexOf("\\$expr\\$");
      method = match.indexOf("\\$method\\$");
      receiver = match.indexOf("\\$receiver\\$");
      return new A.TypeErrorDecoder(message.replace(new RegExp("\\\\\\$arguments\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$argumentsExpr\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$expr\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$method\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$receiver\\\\\\$", "g"), "((?:x|[^x])*)"), $arguments, argumentsExpr, expr, method, receiver);
    },
    TypeErrorDecoder_provokeCallErrorOn(expression) {
      return function($expr$) {
        var $argumentsExpr$ = "$arguments$";
        try {
          $expr$.$method$($argumentsExpr$);
        } catch (e) {
          return e.message;
        }
      }(expression);
    },
    TypeErrorDecoder_provokePropertyErrorOn(expression) {
      return function($expr$) {
        try {
          $expr$.$method$;
        } catch (e) {
          return e.message;
        }
      }(expression);
    },
    JsNoSuchMethodError$(_message, match) {
      var t1 = match == null,
        t2 = t1 ? null : match.method;
      return new A.JsNoSuchMethodError(_message, t2, t1 ? null : match.receiver);
    },
    unwrapException(ex) {
      var t1;
      if (ex == null)
        return new A.NullThrownFromJavaScriptException(ex);
      if (ex instanceof A.ExceptionAndStackTrace) {
        t1 = ex.dartException;
        return A.saveStackTrace(ex, t1 == null ? type$.Object._as(t1) : t1);
      }
      if (typeof ex !== "object")
        return ex;
      if ("dartException" in ex)
        return A.saveStackTrace(ex, ex.dartException);
      return A._unwrapNonDartException(ex);
    },
    saveStackTrace(ex, error) {
      if (type$.Error._is(error))
        if (error.$thrownJsError == null)
          error.$thrownJsError = ex;
      return error;
    },
    _unwrapNonDartException(ex) {
      var message, number, ieErrorCode, nsme, notClosure, nullCall, nullLiteralCall, undefCall, undefLiteralCall, nullProperty, undefProperty, undefLiteralProperty, match;
      if (!("message" in ex))
        return ex;
      message = ex.message;
      if ("number" in ex && typeof ex.number == "number") {
        number = ex.number;
        ieErrorCode = number & 65535;
        if ((B.JSInt_methods._shrOtherPositive$1(number, 16) & 8191) === 10)
          switch (ieErrorCode) {
            case 438:
              return A.saveStackTrace(ex, A.JsNoSuchMethodError$(A.S(message) + " (Error " + ieErrorCode + ")", null));
            case 445:
            case 5007:
              A.S(message);
              return A.saveStackTrace(ex, new A.NullError());
          }
      }
      if (ex instanceof TypeError) {
        nsme = $.$get$TypeErrorDecoder_noSuchMethodPattern();
        notClosure = $.$get$TypeErrorDecoder_notClosurePattern();
        nullCall = $.$get$TypeErrorDecoder_nullCallPattern();
        nullLiteralCall = $.$get$TypeErrorDecoder_nullLiteralCallPattern();
        undefCall = $.$get$TypeErrorDecoder_undefinedCallPattern();
        undefLiteralCall = $.$get$TypeErrorDecoder_undefinedLiteralCallPattern();
        nullProperty = $.$get$TypeErrorDecoder_nullPropertyPattern();
        $.$get$TypeErrorDecoder_nullLiteralPropertyPattern();
        undefProperty = $.$get$TypeErrorDecoder_undefinedPropertyPattern();
        undefLiteralProperty = $.$get$TypeErrorDecoder_undefinedLiteralPropertyPattern();
        match = nsme.matchTypeError$1(message);
        if (match != null)
          return A.saveStackTrace(ex, A.JsNoSuchMethodError$(A._asString(message), match));
        else {
          match = notClosure.matchTypeError$1(message);
          if (match != null) {
            match.method = "call";
            return A.saveStackTrace(ex, A.JsNoSuchMethodError$(A._asString(message), match));
          } else if (nullCall.matchTypeError$1(message) != null || nullLiteralCall.matchTypeError$1(message) != null || undefCall.matchTypeError$1(message) != null || undefLiteralCall.matchTypeError$1(message) != null || nullProperty.matchTypeError$1(message) != null || nullLiteralCall.matchTypeError$1(message) != null || undefProperty.matchTypeError$1(message) != null || undefLiteralProperty.matchTypeError$1(message) != null) {
            A._asString(message);
            return A.saveStackTrace(ex, new A.NullError());
          }
        }
        return A.saveStackTrace(ex, new A.UnknownJsTypeError(typeof message == "string" ? message : ""));
      }
      if (ex instanceof RangeError) {
        if (typeof message == "string" && message.indexOf("call stack") !== -1)
          return new A.StackOverflowError();
        message = function(ex) {
          try {
            return String(ex);
          } catch (e) {
          }
          return null;
        }(ex);
        return A.saveStackTrace(ex, new A.ArgumentError(false, null, null, typeof message == "string" ? message.replace(/^RangeError:\s*/, "") : message));
      }
      if (typeof InternalError == "function" && ex instanceof InternalError)
        if (typeof message == "string" && message === "too much recursion")
          return new A.StackOverflowError();
      return ex;
    },
    getTraceFromException(exception) {
      var trace;
      if (exception instanceof A.ExceptionAndStackTrace)
        return exception.stackTrace;
      if (exception == null)
        return new A._StackTrace(exception);
      trace = exception.$cachedTrace;
      if (trace != null)
        return trace;
      trace = new A._StackTrace(exception);
      if (typeof exception === "object")
        exception.$cachedTrace = trace;
      return trace;
    },
    objectHashCode(object) {
      if (object == null)
        return J.get$hashCode$(object);
      if (typeof object == "object")
        return A.Primitives_objectHashCode(object);
      return J.get$hashCode$(object);
    },
    fillLiteralMap(keyValuePairs, result) {
      var index, index0, index1,
        $length = keyValuePairs.length;
      for (index = 0; index < $length; index = index1) {
        index0 = index + 1;
        index1 = index0 + 1;
        result.$indexSet(0, keyValuePairs[index], keyValuePairs[index0]);
      }
      return result;
    },
    _invokeClosure(closure, numberOfArguments, arg1, arg2, arg3, arg4) {
      type$.Function._as(closure);
      switch (A._asInt(numberOfArguments)) {
        case 0:
          return closure.call$0();
        case 1:
          return closure.call$1(arg1);
        case 2:
          return closure.call$2(arg1, arg2);
        case 3:
          return closure.call$3(arg1, arg2, arg3);
        case 4:
          return closure.call$4(arg1, arg2, arg3, arg4);
      }
      throw A.wrapException(A.Exception_Exception("Unsupported number of arguments for wrapped closure"));
    },
    convertDartClosureToJS(closure, arity) {
      var $function = closure.$identity;
      if (!!$function)
        return $function;
      $function = A.convertDartClosureToJSUncached(closure, arity);
      closure.$identity = $function;
      return $function;
    },
    convertDartClosureToJSUncached(closure, arity) {
      var entry;
      switch (arity) {
        case 0:
          entry = closure.call$0;
          break;
        case 1:
          entry = closure.call$1;
          break;
        case 2:
          entry = closure.call$2;
          break;
        case 3:
          entry = closure.call$3;
          break;
        case 4:
          entry = closure.call$4;
          break;
        default:
          entry = null;
      }
      if (entry != null)
        return entry.bind(closure);
      return function(closure, arity, invoke) {
        return function(a1, a2, a3, a4) {
          return invoke(closure, arity, a1, a2, a3, a4);
        };
      }(closure, arity, A._invokeClosure);
    },
    Closure_fromTearOff(parameters) {
      var $prototype, $constructor, t2, trampoline, applyTrampoline, i, stub, stub0, stubName, stubCallName,
        container = parameters.co,
        isStatic = parameters.iS,
        isIntercepted = parameters.iI,
        needsDirectAccess = parameters.nDA,
        applyTrampolineIndex = parameters.aI,
        funsOrNames = parameters.fs,
        callNames = parameters.cs,
        $name = funsOrNames[0],
        callName = callNames[0],
        $function = container[$name],
        t1 = parameters.fT;
      t1.toString;
      $prototype = isStatic ? Object.create(new A.StaticClosure().constructor.prototype) : Object.create(new A.BoundClosure(null, null).constructor.prototype);
      $prototype.$initialize = $prototype.constructor;
      $constructor = isStatic ? function static_tear_off() {
        this.$initialize();
      } : function tear_off(a, b) {
        this.$initialize(a, b);
      };
      $prototype.constructor = $constructor;
      $constructor.prototype = $prototype;
      $prototype.$_name = $name;
      $prototype.$_target = $function;
      t2 = !isStatic;
      if (t2)
        trampoline = A.Closure_forwardCallTo($name, $function, isIntercepted, needsDirectAccess);
      else {
        $prototype.$static_name = $name;
        trampoline = $function;
      }
      $prototype.$signature = A.Closure__computeSignatureFunctionNewRti(t1, isStatic, isIntercepted);
      $prototype[callName] = trampoline;
      for (applyTrampoline = trampoline, i = 1; i < funsOrNames.length; ++i) {
        stub = funsOrNames[i];
        if (typeof stub == "string") {
          stub0 = container[stub];
          stubName = stub;
          stub = stub0;
        } else
          stubName = "";
        stubCallName = callNames[i];
        if (stubCallName != null) {
          if (t2)
            stub = A.Closure_forwardCallTo(stubName, stub, isIntercepted, needsDirectAccess);
          $prototype[stubCallName] = stub;
        }
        if (i === applyTrampolineIndex)
          applyTrampoline = stub;
      }
      $prototype["call*"] = applyTrampoline;
      $prototype.$requiredArgCount = parameters.rC;
      $prototype.$defaultValues = parameters.dV;
      return $constructor;
    },
    Closure__computeSignatureFunctionNewRti(functionType, isStatic, isIntercepted) {
      if (typeof functionType == "number")
        return functionType;
      if (typeof functionType == "string") {
        if (isStatic)
          throw A.wrapException("Cannot compute signature for static tearoff.");
        return function(recipe, evalOnReceiver) {
          return function() {
            return evalOnReceiver(this, recipe);
          };
        }(functionType, A.BoundClosure_evalRecipe);
      }
      throw A.wrapException("Error in functionType of tearoff");
    },
    Closure_cspForwardCall(arity, needsDirectAccess, stubName, $function) {
      var getReceiver = A.BoundClosure_receiverOf;
      switch (needsDirectAccess ? -1 : arity) {
        case 0:
          return function(entry, receiverOf) {
            return function() {
              return receiverOf(this)[entry]();
            };
          }(stubName, getReceiver);
        case 1:
          return function(entry, receiverOf) {
            return function(a) {
              return receiverOf(this)[entry](a);
            };
          }(stubName, getReceiver);
        case 2:
          return function(entry, receiverOf) {
            return function(a, b) {
              return receiverOf(this)[entry](a, b);
            };
          }(stubName, getReceiver);
        case 3:
          return function(entry, receiverOf) {
            return function(a, b, c) {
              return receiverOf(this)[entry](a, b, c);
            };
          }(stubName, getReceiver);
        case 4:
          return function(entry, receiverOf) {
            return function(a, b, c, d) {
              return receiverOf(this)[entry](a, b, c, d);
            };
          }(stubName, getReceiver);
        case 5:
          return function(entry, receiverOf) {
            return function(a, b, c, d, e) {
              return receiverOf(this)[entry](a, b, c, d, e);
            };
          }(stubName, getReceiver);
        default:
          return function(f, receiverOf) {
            return function() {
              return f.apply(receiverOf(this), arguments);
            };
          }($function, getReceiver);
      }
    },
    Closure_forwardCallTo(stubName, $function, isIntercepted, needsDirectAccess) {
      if (isIntercepted)
        return A.Closure_forwardInterceptedCallTo(stubName, $function, needsDirectAccess);
      return A.Closure_cspForwardCall($function.length, needsDirectAccess, stubName, $function);
    },
    Closure_cspForwardInterceptedCall(arity, needsDirectAccess, stubName, $function) {
      var getReceiver = A.BoundClosure_receiverOf,
        getInterceptor = A.BoundClosure_interceptorOf;
      switch (needsDirectAccess ? -1 : arity) {
        case 0:
          throw A.wrapException(new A.RuntimeError("Intercepted function with no arguments."));
        case 1:
          return function(entry, interceptorOf, receiverOf) {
            return function() {
              return interceptorOf(this)[entry](receiverOf(this));
            };
          }(stubName, getInterceptor, getReceiver);
        case 2:
          return function(entry, interceptorOf, receiverOf) {
            return function(a) {
              return interceptorOf(this)[entry](receiverOf(this), a);
            };
          }(stubName, getInterceptor, getReceiver);
        case 3:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b) {
              return interceptorOf(this)[entry](receiverOf(this), a, b);
            };
          }(stubName, getInterceptor, getReceiver);
        case 4:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b, c) {
              return interceptorOf(this)[entry](receiverOf(this), a, b, c);
            };
          }(stubName, getInterceptor, getReceiver);
        case 5:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b, c, d) {
              return interceptorOf(this)[entry](receiverOf(this), a, b, c, d);
            };
          }(stubName, getInterceptor, getReceiver);
        case 6:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b, c, d, e) {
              return interceptorOf(this)[entry](receiverOf(this), a, b, c, d, e);
            };
          }(stubName, getInterceptor, getReceiver);
        default:
          return function(f, interceptorOf, receiverOf) {
            return function() {
              var a = [receiverOf(this)];
              Array.prototype.push.apply(a, arguments);
              return f.apply(interceptorOf(this), a);
            };
          }($function, getInterceptor, getReceiver);
      }
    },
    Closure_forwardInterceptedCallTo(stubName, $function, needsDirectAccess) {
      var arity, t1;
      if ($.BoundClosure__interceptorFieldNameCache == null)
        $.BoundClosure__interceptorFieldNameCache = A.BoundClosure__computeFieldNamed("interceptor");
      if ($.BoundClosure__receiverFieldNameCache == null)
        $.BoundClosure__receiverFieldNameCache = A.BoundClosure__computeFieldNamed("receiver");
      arity = $function.length;
      t1 = A.Closure_cspForwardInterceptedCall(arity, needsDirectAccess, stubName, $function);
      return t1;
    },
    closureFromTearOff(parameters) {
      return A.Closure_fromTearOff(parameters);
    },
    BoundClosure_evalRecipe(closure, recipe) {
      return A._Universe_evalInEnvironment(init.typeUniverse, A.instanceType(closure._receiver), recipe);
    },
    BoundClosure_receiverOf(closure) {
      return closure._receiver;
    },
    BoundClosure_interceptorOf(closure) {
      return closure._interceptor;
    },
    BoundClosure__computeFieldNamed(fieldName) {
      var names, i, $name,
        template = new A.BoundClosure("receiver", "interceptor"),
        t1 = Object.getOwnPropertyNames(template);
      t1.$flags = 1;
      names = t1;
      for (t1 = names.length, i = 0; i < t1; ++i) {
        $name = names[i];
        if (template[$name] === fieldName)
          return $name;
      }
      throw A.wrapException(A.ArgumentError$("Field name " + fieldName + " not found.", null));
    },
    boolConversionCheck(value) {
      if (value == null)
        A.assertThrow("boolean expression must not be null");
      return value;
    },
    assertThrow(message) {
      throw A.wrapException(new A._AssertionError(message));
    },
    throwCyclicInit(staticName) {
      throw A.wrapException(new A._CyclicInitializationError(staticName));
    },
    getIsolateAffinityTag($name) {
      return init.getIsolateTag($name);
    },
    defineProperty(obj, property, value) {
      Object.defineProperty(obj, property, {value: value, enumerable: false, writable: true, configurable: true});
    },
    lookupAndCacheInterceptor(obj) {
      var interceptor, interceptorClass, altTag, mark, t1,
        tag = A._asString($.getTagFunction.call$1(obj)),
        record = $.dispatchRecordsForInstanceTags[tag];
      if (record != null) {
        Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
        return record.i;
      }
      interceptor = $.interceptorsForUncacheableTags[tag];
      if (interceptor != null)
        return interceptor;
      interceptorClass = init.interceptorsByTag[tag];
      if (interceptorClass == null) {
        altTag = A._asStringQ($.alternateTagFunction.call$2(obj, tag));
        if (altTag != null) {
          record = $.dispatchRecordsForInstanceTags[altTag];
          if (record != null) {
            Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
            return record.i;
          }
          interceptor = $.interceptorsForUncacheableTags[altTag];
          if (interceptor != null)
            return interceptor;
          interceptorClass = init.interceptorsByTag[altTag];
          tag = altTag;
        }
      }
      if (interceptorClass == null)
        return null;
      interceptor = interceptorClass.prototype;
      mark = tag[0];
      if (mark === "!") {
        record = A.makeLeafDispatchRecord(interceptor);
        $.dispatchRecordsForInstanceTags[tag] = record;
        Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
        return record.i;
      }
      if (mark === "~") {
        $.interceptorsForUncacheableTags[tag] = interceptor;
        return interceptor;
      }
      if (mark === "-") {
        t1 = A.makeLeafDispatchRecord(interceptor);
        Object.defineProperty(Object.getPrototypeOf(obj), init.dispatchPropertyName, {value: t1, enumerable: false, writable: true, configurable: true});
        return t1.i;
      }
      if (mark === "+")
        return A.patchInteriorProto(obj, interceptor);
      if (mark === "*")
        throw A.wrapException(A.UnimplementedError$(tag));
      if (init.leafTags[tag] === true) {
        t1 = A.makeLeafDispatchRecord(interceptor);
        Object.defineProperty(Object.getPrototypeOf(obj), init.dispatchPropertyName, {value: t1, enumerable: false, writable: true, configurable: true});
        return t1.i;
      } else
        return A.patchInteriorProto(obj, interceptor);
    },
    patchInteriorProto(obj, interceptor) {
      var proto = Object.getPrototypeOf(obj);
      Object.defineProperty(proto, init.dispatchPropertyName, {value: J.makeDispatchRecord(interceptor, proto, null, null), enumerable: false, writable: true, configurable: true});
      return interceptor;
    },
    makeLeafDispatchRecord(interceptor) {
      return J.makeDispatchRecord(interceptor, false, null, !!interceptor.$isJavaScriptIndexingBehavior);
    },
    makeDefaultDispatchRecord(tag, interceptorClass, proto) {
      var interceptor = interceptorClass.prototype;
      if (init.leafTags[tag] === true)
        return A.makeLeafDispatchRecord(interceptor);
      else
        return J.makeDispatchRecord(interceptor, proto, null, null);
    },
    initNativeDispatch() {
      if (true === $.initNativeDispatchFlag)
        return;
      $.initNativeDispatchFlag = true;
      A.initNativeDispatchContinue();
    },
    initNativeDispatchContinue() {
      var map, tags, fun, i, tag, proto, record, interceptorClass;
      $.dispatchRecordsForInstanceTags = Object.create(null);
      $.interceptorsForUncacheableTags = Object.create(null);
      A.initHooks();
      map = init.interceptorsByTag;
      tags = Object.getOwnPropertyNames(map);
      if (typeof window != "undefined") {
        window;
        fun = function() {
        };
        for (i = 0; i < tags.length; ++i) {
          tag = tags[i];
          proto = $.prototypeForTagFunction.call$1(tag);
          if (proto != null) {
            record = A.makeDefaultDispatchRecord(tag, map[tag], proto);
            if (record != null) {
              Object.defineProperty(proto, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
              fun.prototype = proto;
            }
          }
        }
      }
      for (i = 0; i < tags.length; ++i) {
        tag = tags[i];
        if (/^[A-Za-z_]/.test(tag)) {
          interceptorClass = map[tag];
          map["!" + tag] = interceptorClass;
          map["~" + tag] = interceptorClass;
          map["-" + tag] = interceptorClass;
          map["+" + tag] = interceptorClass;
          map["*" + tag] = interceptorClass;
        }
      }
    },
    initHooks() {
      var transformers, i, transformer, getTag, getUnknownTag, prototypeForTag,
        hooks = B.C_JS_CONST0();
      hooks = A.applyHooksTransformer(B.C_JS_CONST1, A.applyHooksTransformer(B.C_JS_CONST2, A.applyHooksTransformer(B.C_JS_CONST3, A.applyHooksTransformer(B.C_JS_CONST3, A.applyHooksTransformer(B.C_JS_CONST4, A.applyHooksTransformer(B.C_JS_CONST5, A.applyHooksTransformer(B.C_JS_CONST6(B.C_JS_CONST), hooks)))))));
      if (typeof dartNativeDispatchHooksTransformer != "undefined") {
        transformers = dartNativeDispatchHooksTransformer;
        if (typeof transformers == "function")
          transformers = [transformers];
        if (Array.isArray(transformers))
          for (i = 0; i < transformers.length; ++i) {
            transformer = transformers[i];
            if (typeof transformer == "function")
              hooks = transformer(hooks) || hooks;
          }
      }
      getTag = hooks.getTag;
      getUnknownTag = hooks.getUnknownTag;
      prototypeForTag = hooks.prototypeForTag;
      $.getTagFunction = new A.initHooks_closure(getTag);
      $.alternateTagFunction = new A.initHooks_closure0(getUnknownTag);
      $.prototypeForTagFunction = new A.initHooks_closure1(prototypeForTag);
    },
    applyHooksTransformer(transformer, hooks) {
      return transformer(hooks) || hooks;
    },
    createRecordTypePredicate(shape, fieldRtis) {
      var $length = fieldRtis.length,
        $function = init.rttc["" + $length + ";" + shape];
      if ($function == null)
        return null;
      if ($length === 0)
        return $function;
      if ($length === $function.length)
        return $function.apply(null, fieldRtis);
      return $function(fieldRtis);
    },
    quoteStringForRegExp(string) {
      if (/[[\]{}()*+?.\\^$|]/.test(string))
        return string.replace(/[[\]{}()*+?.\\^$|]/g, "\\$&");
      return string;
    },
    ConstantMapView: function ConstantMapView(t0, t1) {
      this._collection$_map = t0;
      this.$ti = t1;
    },
    ConstantMap: function ConstantMap() {
    },
    ConstantStringMap: function ConstantStringMap(t0, t1, t2) {
      this._jsIndex = t0;
      this._values = t1;
      this.$ti = t2;
    },
    _KeysOrValues: function _KeysOrValues(t0, t1) {
      this._elements = t0;
      this.$ti = t1;
    },
    _KeysOrValuesOrElementsIterator: function _KeysOrValuesOrElementsIterator(t0, t1, t2) {
      var _ = this;
      _._elements = t0;
      _.__js_helper$_length = t1;
      _.__js_helper$_index = 0;
      _.__js_helper$_current = null;
      _.$ti = t2;
    },
    JSInvocationMirror: function JSInvocationMirror(t0, t1, t2, t3, t4) {
      var _ = this;
      _._memberName = t0;
      _.__js_helper$_kind = t1;
      _._arguments = t2;
      _._namedArgumentNames = t3;
      _._typeArgumentCount = t4;
    },
    Primitives_functionNoSuchMethod_closure: function Primitives_functionNoSuchMethod_closure(t0, t1, t2) {
      this._box_0 = t0;
      this.namedArgumentList = t1;
      this.$arguments = t2;
    },
    TypeErrorDecoder: function TypeErrorDecoder(t0, t1, t2, t3, t4, t5) {
      var _ = this;
      _._pattern = t0;
      _._arguments = t1;
      _._argumentsExpr = t2;
      _._expr = t3;
      _._method = t4;
      _._receiver = t5;
    },
    NullError: function NullError() {
    },
    JsNoSuchMethodError: function JsNoSuchMethodError(t0, t1, t2) {
      this.__js_helper$_message = t0;
      this._method = t1;
      this._receiver = t2;
    },
    UnknownJsTypeError: function UnknownJsTypeError(t0) {
      this.__js_helper$_message = t0;
    },
    NullThrownFromJavaScriptException: function NullThrownFromJavaScriptException(t0) {
      this._irritant = t0;
    },
    ExceptionAndStackTrace: function ExceptionAndStackTrace(t0, t1) {
      this.dartException = t0;
      this.stackTrace = t1;
    },
    _StackTrace: function _StackTrace(t0) {
      this._exception = t0;
      this._trace = null;
    },
    Closure: function Closure() {
    },
    Closure0Args: function Closure0Args() {
    },
    Closure2Args: function Closure2Args() {
    },
    TearOffClosure: function TearOffClosure() {
    },
    StaticClosure: function StaticClosure() {
    },
    BoundClosure: function BoundClosure(t0, t1) {
      this._receiver = t0;
      this._interceptor = t1;
    },
    _CyclicInitializationError: function _CyclicInitializationError(t0) {
      this.variableName = t0;
    },
    RuntimeError: function RuntimeError(t0) {
      this.message = t0;
    },
    _AssertionError: function _AssertionError(t0) {
      this.message = t0;
    },
    _Required: function _Required() {
    },
    JsLinkedHashMap: function JsLinkedHashMap(t0) {
      var _ = this;
      _.__js_helper$_length = 0;
      _._last = _._first = _.__js_helper$_rest = _._nums = _._strings = null;
      _._modifications = 0;
      _.$ti = t0;
    },
    LinkedHashMapCell: function LinkedHashMapCell(t0, t1) {
      var _ = this;
      _.hashMapCellKey = t0;
      _.hashMapCellValue = t1;
      _._previous = _._next = null;
    },
    LinkedHashMapKeysIterable: function LinkedHashMapKeysIterable(t0, t1) {
      this._map = t0;
      this.$ti = t1;
    },
    LinkedHashMapKeyIterator: function LinkedHashMapKeyIterator(t0, t1, t2, t3) {
      var _ = this;
      _._map = t0;
      _._modifications = t1;
      _._cell = t2;
      _.__js_helper$_current = null;
      _.$ti = t3;
    },
    initHooks_closure: function initHooks_closure(t0) {
      this.getTag = t0;
    },
    initHooks_closure0: function initHooks_closure0(t0) {
      this.getUnknownTag = t0;
    },
    initHooks_closure1: function initHooks_closure1(t0) {
      this.prototypeForTag = t0;
    },
    _ensureNativeList(list) {
      return list;
    },
    NativeByteData_NativeByteData($length) {
      return new DataView(new ArrayBuffer($length));
    },
    NativeUint8List_NativeUint8List($length) {
      return new Uint8Array($length);
    },
    NativeUint8List_NativeUint8List$view(buffer, offsetInBytes, $length) {
      return $length == null ? new Uint8Array(buffer, offsetInBytes) : new Uint8Array(buffer, offsetInBytes, $length);
    },
    _checkValidIndex(index, list, $length) {
      if (index >>> 0 !== index || index >= $length)
        throw A.wrapException(A.diagnoseIndexError(list, index));
    },
    _checkValidRange(start, end, $length) {
      var t1;
      if (!(start >>> 0 !== start))
        if (end == null)
          t1 = start > $length;
        else
          t1 = end >>> 0 !== end || start > end || end > $length;
      else
        t1 = true;
      if (t1)
        throw A.wrapException(A.diagnoseRangeError(start, end, $length));
      if (end == null)
        return $length;
      return end;
    },
    NativeByteBuffer: function NativeByteBuffer() {
    },
    NativeTypedData: function NativeTypedData() {
    },
    _UnmodifiableNativeByteBufferView: function _UnmodifiableNativeByteBufferView(t0) {
      this._data = t0;
    },
    NativeByteData: function NativeByteData() {
    },
    NativeTypedArray: function NativeTypedArray() {
    },
    NativeTypedArrayOfDouble: function NativeTypedArrayOfDouble() {
    },
    NativeTypedArrayOfInt: function NativeTypedArrayOfInt() {
    },
    NativeFloat32List: function NativeFloat32List() {
    },
    NativeFloat64List: function NativeFloat64List() {
    },
    NativeInt16List: function NativeInt16List() {
    },
    NativeInt32List: function NativeInt32List() {
    },
    NativeInt8List: function NativeInt8List() {
    },
    NativeUint16List: function NativeUint16List() {
    },
    NativeUint32List: function NativeUint32List() {
    },
    NativeUint8ClampedList: function NativeUint8ClampedList() {
    },
    NativeUint8List: function NativeUint8List() {
    },
    _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin: function _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin() {
    },
    _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin: function _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin() {
    },
    _NativeTypedArrayOfInt_NativeTypedArray_ListMixin: function _NativeTypedArrayOfInt_NativeTypedArray_ListMixin() {
    },
    _NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin: function _NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin() {
    },
    Rti__getQuestionFromStar(universe, rti) {
      var question = rti._precomputed1;
      return question == null ? rti._precomputed1 = A._Universe__lookupQuestionRti(universe, rti._primary, true) : question;
    },
    Rti__getFutureFromFutureOr(universe, rti) {
      var future = rti._precomputed1;
      return future == null ? rti._precomputed1 = A._Universe__lookupInterfaceRti(universe, "Future", [rti._primary]) : future;
    },
    Rti__isUnionOfFunctionType(rti) {
      var kind = rti._kind;
      if (kind === 6 || kind === 7 || kind === 8)
        return A.Rti__isUnionOfFunctionType(rti._primary);
      return kind === 12 || kind === 13;
    },
    Rti__getCanonicalRecipe(rti) {
      return rti._canonicalRecipe;
    },
    findType(recipe) {
      return A._Universe_eval(init.typeUniverse, recipe, false);
    },
    _substitute(universe, rti, typeArguments, depth) {
      var baseType, substitutedBaseType, interfaceTypeArguments, substitutedInterfaceTypeArguments, base, substitutedBase, $arguments, substitutedArguments, t1, fields, substitutedFields, returnType, substitutedReturnType, functionParameters, substitutedFunctionParameters, bounds, substitutedBounds, index, argument,
        kind = rti._kind;
      switch (kind) {
        case 5:
        case 1:
        case 2:
        case 3:
        case 4:
          return rti;
        case 6:
          baseType = rti._primary;
          substitutedBaseType = A._substitute(universe, baseType, typeArguments, depth);
          if (substitutedBaseType === baseType)
            return rti;
          return A._Universe__lookupStarRti(universe, substitutedBaseType, true);
        case 7:
          baseType = rti._primary;
          substitutedBaseType = A._substitute(universe, baseType, typeArguments, depth);
          if (substitutedBaseType === baseType)
            return rti;
          return A._Universe__lookupQuestionRti(universe, substitutedBaseType, true);
        case 8:
          baseType = rti._primary;
          substitutedBaseType = A._substitute(universe, baseType, typeArguments, depth);
          if (substitutedBaseType === baseType)
            return rti;
          return A._Universe__lookupFutureOrRti(universe, substitutedBaseType, true);
        case 9:
          interfaceTypeArguments = rti._rest;
          substitutedInterfaceTypeArguments = A._substituteArray(universe, interfaceTypeArguments, typeArguments, depth);
          if (substitutedInterfaceTypeArguments === interfaceTypeArguments)
            return rti;
          return A._Universe__lookupInterfaceRti(universe, rti._primary, substitutedInterfaceTypeArguments);
        case 10:
          base = rti._primary;
          substitutedBase = A._substitute(universe, base, typeArguments, depth);
          $arguments = rti._rest;
          substitutedArguments = A._substituteArray(universe, $arguments, typeArguments, depth);
          if (substitutedBase === base && substitutedArguments === $arguments)
            return rti;
          return A._Universe__lookupBindingRti(universe, substitutedBase, substitutedArguments);
        case 11:
          t1 = rti._primary;
          fields = rti._rest;
          substitutedFields = A._substituteArray(universe, fields, typeArguments, depth);
          if (substitutedFields === fields)
            return rti;
          return A._Universe__lookupRecordRti(universe, t1, substitutedFields);
        case 12:
          returnType = rti._primary;
          substitutedReturnType = A._substitute(universe, returnType, typeArguments, depth);
          functionParameters = rti._rest;
          substitutedFunctionParameters = A._substituteFunctionParameters(universe, functionParameters, typeArguments, depth);
          if (substitutedReturnType === returnType && substitutedFunctionParameters === functionParameters)
            return rti;
          return A._Universe__lookupFunctionRti(universe, substitutedReturnType, substitutedFunctionParameters);
        case 13:
          bounds = rti._rest;
          depth += bounds.length;
          substitutedBounds = A._substituteArray(universe, bounds, typeArguments, depth);
          base = rti._primary;
          substitutedBase = A._substitute(universe, base, typeArguments, depth);
          if (substitutedBounds === bounds && substitutedBase === base)
            return rti;
          return A._Universe__lookupGenericFunctionRti(universe, substitutedBase, substitutedBounds, true);
        case 14:
          index = rti._primary;
          if (index < depth)
            return rti;
          argument = typeArguments[index - depth];
          if (argument == null)
            return rti;
          return argument;
        default:
          throw A.wrapException(A.AssertionError$("Attempted to substitute unexpected RTI kind " + kind));
      }
    },
    _substituteArray(universe, rtiArray, typeArguments, depth) {
      var changed, i, rti, substitutedRti,
        $length = rtiArray.length,
        result = A._Utils_newArrayOrEmpty($length);
      for (changed = false, i = 0; i < $length; ++i) {
        rti = rtiArray[i];
        substitutedRti = A._substitute(universe, rti, typeArguments, depth);
        if (substitutedRti !== rti)
          changed = true;
        result[i] = substitutedRti;
      }
      return changed ? result : rtiArray;
    },
    _substituteNamed(universe, namedArray, typeArguments, depth) {
      var changed, i, t1, t2, rti, substitutedRti,
        $length = namedArray.length,
        result = A._Utils_newArrayOrEmpty($length);
      for (changed = false, i = 0; i < $length; i += 3) {
        t1 = namedArray[i];
        t2 = namedArray[i + 1];
        rti = namedArray[i + 2];
        substitutedRti = A._substitute(universe, rti, typeArguments, depth);
        if (substitutedRti !== rti)
          changed = true;
        result.splice(i, 3, t1, t2, substitutedRti);
      }
      return changed ? result : namedArray;
    },
    _substituteFunctionParameters(universe, functionParameters, typeArguments, depth) {
      var result,
        requiredPositional = functionParameters._requiredPositional,
        substitutedRequiredPositional = A._substituteArray(universe, requiredPositional, typeArguments, depth),
        optionalPositional = functionParameters._optionalPositional,
        substitutedOptionalPositional = A._substituteArray(universe, optionalPositional, typeArguments, depth),
        named = functionParameters._named,
        substitutedNamed = A._substituteNamed(universe, named, typeArguments, depth);
      if (substitutedRequiredPositional === requiredPositional && substitutedOptionalPositional === optionalPositional && substitutedNamed === named)
        return functionParameters;
      result = new A._FunctionParameters();
      result._requiredPositional = substitutedRequiredPositional;
      result._optionalPositional = substitutedOptionalPositional;
      result._named = substitutedNamed;
      return result;
    },
    _setArrayType(target, rti) {
      target[init.arrayRti] = rti;
      return target;
    },
    closureFunctionType(closure) {
      var signature = closure.$signature;
      if (signature != null) {
        if (typeof signature == "number")
          return A.getTypeFromTypesTable(signature);
        return closure.$signature();
      }
      return null;
    },
    instanceOrFunctionType(object, testRti) {
      var rti;
      if (A.Rti__isUnionOfFunctionType(testRti))
        if (object instanceof A.Closure) {
          rti = A.closureFunctionType(object);
          if (rti != null)
            return rti;
        }
      return A.instanceType(object);
    },
    instanceType(object) {
      if (object instanceof A.Object)
        return A._instanceType(object);
      if (Array.isArray(object))
        return A._arrayInstanceType(object);
      return A._instanceTypeFromConstructor(J.getInterceptor$(object));
    },
    _arrayInstanceType(object) {
      var rti = object[init.arrayRti],
        defaultRti = type$.JSArray_dynamic;
      if (rti == null)
        return defaultRti;
      if (rti.constructor !== defaultRti.constructor)
        return defaultRti;
      return rti;
    },
    _instanceType(object) {
      var rti = object.$ti;
      return rti != null ? rti : A._instanceTypeFromConstructor(object);
    },
    _instanceTypeFromConstructor(instance) {
      var $constructor = instance.constructor,
        probe = $constructor.$ccache;
      if (probe != null)
        return probe;
      return A._instanceTypeFromConstructorMiss(instance, $constructor);
    },
    _instanceTypeFromConstructorMiss(instance, $constructor) {
      var effectiveConstructor = instance instanceof A.Closure ? Object.getPrototypeOf(Object.getPrototypeOf(instance)).constructor : $constructor,
        rti = A._Universe_findErasedType(init.typeUniverse, effectiveConstructor.name);
      $constructor.$ccache = rti;
      return rti;
    },
    getTypeFromTypesTable(index) {
      var rti,
        table = init.types,
        type = table[index];
      if (typeof type == "string") {
        rti = A._Universe_eval(init.typeUniverse, type, false);
        table[index] = rti;
        return rti;
      }
      return type;
    },
    getRuntimeTypeOfDartObject(object) {
      return A.createRuntimeType(A._instanceType(object));
    },
    _structuralTypeOf(object) {
      var functionRti = object instanceof A.Closure ? A.closureFunctionType(object) : null;
      if (functionRti != null)
        return functionRti;
      if (type$.TrustedGetRuntimeType._is(object))
        return J.get$runtimeType$(object)._rti;
      if (Array.isArray(object))
        return A._arrayInstanceType(object);
      return A.instanceType(object);
    },
    createRuntimeType(rti) {
      var t1 = rti._cachedRuntimeType;
      return t1 == null ? rti._cachedRuntimeType = A._createRuntimeType(rti) : t1;
    },
    _createRuntimeType(rti) {
      var starErasedRti, t1,
        s = rti._canonicalRecipe,
        starErasedRecipe = s.replace(/\*/g, "");
      if (starErasedRecipe === s)
        return rti._cachedRuntimeType = new A._Type(rti);
      starErasedRti = A._Universe_eval(init.typeUniverse, starErasedRecipe, true);
      t1 = starErasedRti._cachedRuntimeType;
      return t1 == null ? starErasedRti._cachedRuntimeType = A._createRuntimeType(starErasedRti) : t1;
    },
    typeLiteral(recipe) {
      return A.createRuntimeType(A._Universe_eval(init.typeUniverse, recipe, false));
    },
    _installSpecializedIsTest(object) {
      var t1, unstarred, unstarredKind, isFn, $name, predicate, testRti = this;
      if (testRti === type$.Object)
        return A._finishIsFn(testRti, object, A._isObject);
      if (!A.isSoundTopType(testRti))
        t1 = testRti === type$.legacy_Object;
      else
        t1 = true;
      if (t1)
        return A._finishIsFn(testRti, object, A._isTop);
      t1 = testRti._kind;
      if (t1 === 7)
        return A._finishIsFn(testRti, object, A._generalNullableIsTestImplementation);
      if (t1 === 1)
        return A._finishIsFn(testRti, object, A._isNever);
      unstarred = t1 === 6 ? testRti._primary : testRti;
      unstarredKind = unstarred._kind;
      if (unstarredKind === 8)
        return A._finishIsFn(testRti, object, A._isFutureOr);
      if (unstarred === type$.int)
        isFn = A._isInt;
      else if (unstarred === type$.double || unstarred === type$.num)
        isFn = A._isNum;
      else if (unstarred === type$.String)
        isFn = A._isString;
      else
        isFn = unstarred === type$.bool ? A._isBool : null;
      if (isFn != null)
        return A._finishIsFn(testRti, object, isFn);
      if (unstarredKind === 9) {
        $name = unstarred._primary;
        if (unstarred._rest.every(A.isDefinitelyTopType)) {
          testRti._specializedTestResource = "$is" + $name;
          if ($name === "List")
            return A._finishIsFn(testRti, object, A._isListTestViaProperty);
          return A._finishIsFn(testRti, object, A._isTestViaProperty);
        }
      } else if (unstarredKind === 11) {
        predicate = A.createRecordTypePredicate(unstarred._primary, unstarred._rest);
        return A._finishIsFn(testRti, object, predicate == null ? A._isNever : predicate);
      }
      return A._finishIsFn(testRti, object, A._generalIsTestImplementation);
    },
    _finishIsFn(testRti, object, isFn) {
      testRti._is = isFn;
      return testRti._is(object);
    },
    _installSpecializedAsCheck(object) {
      var t1, testRti = this,
        asFn = A._generalAsCheckImplementation;
      if (!A.isSoundTopType(testRti))
        t1 = testRti === type$.legacy_Object;
      else
        t1 = true;
      if (t1)
        asFn = A._asTop;
      else if (testRti === type$.Object)
        asFn = A._asObject;
      else {
        t1 = A.isNullable(testRti);
        if (t1)
          asFn = A._generalNullableAsCheckImplementation;
      }
      testRti._as = asFn;
      return testRti._as(object);
    },
    _nullIs(testRti) {
      var kind = testRti._kind,
        t1 = true;
      if (!A.isSoundTopType(testRti))
        if (!(testRti === type$.legacy_Object))
          if (!(testRti === type$.legacy_Never))
            if (kind !== 7)
              if (!(kind === 6 && A._nullIs(testRti._primary)))
                t1 = kind === 8 && A._nullIs(testRti._primary) || testRti === type$.Null || testRti === type$.JSNull;
      return t1;
    },
    _generalIsTestImplementation(object) {
      var testRti = this;
      if (object == null)
        return A._nullIs(testRti);
      return A.isSubtype(init.typeUniverse, A.instanceOrFunctionType(object, testRti), testRti);
    },
    _generalNullableIsTestImplementation(object) {
      if (object == null)
        return true;
      return this._primary._is(object);
    },
    _isTestViaProperty(object) {
      var tag, testRti = this;
      if (object == null)
        return A._nullIs(testRti);
      tag = testRti._specializedTestResource;
      if (object instanceof A.Object)
        return !!object[tag];
      return !!J.getInterceptor$(object)[tag];
    },
    _isListTestViaProperty(object) {
      var tag, testRti = this;
      if (object == null)
        return A._nullIs(testRti);
      if (typeof object != "object")
        return false;
      if (Array.isArray(object))
        return true;
      tag = testRti._specializedTestResource;
      if (object instanceof A.Object)
        return !!object[tag];
      return !!J.getInterceptor$(object)[tag];
    },
    _generalAsCheckImplementation(object) {
      var testRti = this;
      if (object == null) {
        if (A.isNullable(testRti))
          return object;
      } else if (testRti._is(object))
        return object;
      A._failedAsCheck(object, testRti);
    },
    _generalNullableAsCheckImplementation(object) {
      var testRti = this;
      if (object == null)
        return object;
      else if (testRti._is(object))
        return object;
      A._failedAsCheck(object, testRti);
    },
    _failedAsCheck(object, testRti) {
      throw A.wrapException(A._TypeError$fromMessage(A._Error_compose(object, A._rtiToString(testRti, null))));
    },
    _Error_compose(object, checkedTypeDescription) {
      return A.Error_safeToString(object) + ": type '" + A._rtiToString(A._structuralTypeOf(object), null) + "' is not a subtype of type '" + checkedTypeDescription + "'";
    },
    _TypeError$fromMessage(message) {
      return new A._TypeError("TypeError: " + message);
    },
    _TypeError__TypeError$forType(object, type) {
      return new A._TypeError("TypeError: " + A._Error_compose(object, type));
    },
    _isFutureOr(object) {
      var testRti = this,
        unstarred = testRti._kind === 6 ? testRti._primary : testRti;
      return unstarred._primary._is(object) || A.Rti__getFutureFromFutureOr(init.typeUniverse, unstarred)._is(object);
    },
    _isObject(object) {
      return object != null;
    },
    _asObject(object) {
      if (object != null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "Object"));
    },
    _isTop(object) {
      return true;
    },
    _asTop(object) {
      return object;
    },
    _isNever(object) {
      return false;
    },
    _isBool(object) {
      return true === object || false === object;
    },
    _asBool(object) {
      if (true === object)
        return true;
      if (false === object)
        return false;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "bool"));
    },
    _asBoolS(object) {
      if (true === object)
        return true;
      if (false === object)
        return false;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "bool"));
    },
    _asBoolQ(object) {
      if (true === object)
        return true;
      if (false === object)
        return false;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "bool?"));
    },
    _asDouble(object) {
      if (typeof object == "number")
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "double"));
    },
    _asDoubleS(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "double"));
    },
    _asDoubleQ(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "double?"));
    },
    _isInt(object) {
      return typeof object == "number" && Math.floor(object) === object;
    },
    _asInt(object) {
      if (typeof object == "number" && Math.floor(object) === object)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "int"));
    },
    _asIntS(object) {
      if (typeof object == "number" && Math.floor(object) === object)
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "int"));
    },
    _asIntQ(object) {
      if (typeof object == "number" && Math.floor(object) === object)
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "int?"));
    },
    _isNum(object) {
      return typeof object == "number";
    },
    _asNum(object) {
      if (typeof object == "number")
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "num"));
    },
    _asNumS(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "num"));
    },
    _asNumQ(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "num?"));
    },
    _isString(object) {
      return typeof object == "string";
    },
    _asString(object) {
      if (typeof object == "string")
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "String"));
    },
    _asStringS(object) {
      if (typeof object == "string")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "String"));
    },
    _asStringQ(object) {
      if (typeof object == "string")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "String?"));
    },
    _rtiArrayToString(array, genericContext) {
      var s, sep, i;
      for (s = "", sep = "", i = 0; i < array.length; ++i, sep = ", ")
        s += sep + A._rtiToString(array[i], genericContext);
      return s;
    },
    _recordRtiToString(recordType, genericContext) {
      var fieldCount, names, namesIndex, s, comma, i,
        partialShape = recordType._primary,
        fields = recordType._rest;
      if ("" === partialShape)
        return "(" + A._rtiArrayToString(fields, genericContext) + ")";
      fieldCount = fields.length;
      names = partialShape.split(",");
      namesIndex = names.length - fieldCount;
      for (s = "(", comma = "", i = 0; i < fieldCount; ++i, comma = ", ") {
        s += comma;
        if (namesIndex === 0)
          s += "{";
        s += A._rtiToString(fields[i], genericContext);
        if (namesIndex >= 0)
          s += " " + names[namesIndex];
        ++namesIndex;
      }
      return s + "})";
    },
    _functionRtiToString(functionType, genericContext, bounds) {
      var boundsLength, offset, i, t1, t2, typeParametersText, typeSep, t3, t4, boundRti, kind, parameters, requiredPositional, requiredPositionalLength, optionalPositional, optionalPositionalLength, named, namedLength, returnTypeText, argumentsText, sep, _s2_ = ", ", outerContextLength = null;
      if (bounds != null) {
        boundsLength = bounds.length;
        if (genericContext == null)
          genericContext = A._setArrayType([], type$.JSArray_String);
        else
          outerContextLength = genericContext.length;
        offset = genericContext.length;
        for (i = boundsLength; i > 0; --i)
          B.JSArray_methods.add$1(genericContext, "T" + (offset + i));
        for (t1 = type$.nullable_Object, t2 = type$.legacy_Object, typeParametersText = "<", typeSep = "", i = 0; i < boundsLength; ++i, typeSep = _s2_) {
          t3 = genericContext.length;
          t4 = t3 - 1 - i;
          if (!(t4 >= 0))
            return A.ioore(genericContext, t4);
          typeParametersText = typeParametersText + typeSep + genericContext[t4];
          boundRti = bounds[i];
          kind = boundRti._kind;
          if (!(kind === 2 || kind === 3 || kind === 4 || kind === 5 || boundRti === t1))
            t3 = boundRti === t2;
          else
            t3 = true;
          if (!t3)
            typeParametersText += " extends " + A._rtiToString(boundRti, genericContext);
        }
        typeParametersText += ">";
      } else
        typeParametersText = "";
      t1 = functionType._primary;
      parameters = functionType._rest;
      requiredPositional = parameters._requiredPositional;
      requiredPositionalLength = requiredPositional.length;
      optionalPositional = parameters._optionalPositional;
      optionalPositionalLength = optionalPositional.length;
      named = parameters._named;
      namedLength = named.length;
      returnTypeText = A._rtiToString(t1, genericContext);
      for (argumentsText = "", sep = "", i = 0; i < requiredPositionalLength; ++i, sep = _s2_)
        argumentsText += sep + A._rtiToString(requiredPositional[i], genericContext);
      if (optionalPositionalLength > 0) {
        argumentsText += sep + "[";
        for (sep = "", i = 0; i < optionalPositionalLength; ++i, sep = _s2_)
          argumentsText += sep + A._rtiToString(optionalPositional[i], genericContext);
        argumentsText += "]";
      }
      if (namedLength > 0) {
        argumentsText += sep + "{";
        for (sep = "", i = 0; i < namedLength; i += 3, sep = _s2_) {
          argumentsText += sep;
          if (named[i + 1])
            argumentsText += "required ";
          argumentsText += A._rtiToString(named[i + 2], genericContext) + " " + named[i];
        }
        argumentsText += "}";
      }
      if (outerContextLength != null) {
        genericContext.toString;
        genericContext.length = outerContextLength;
      }
      return typeParametersText + "(" + argumentsText + ") => " + returnTypeText;
    },
    _rtiToString(rti, genericContext) {
      var questionArgument, s, argumentKind, $name, $arguments, t1, t2,
        kind = rti._kind;
      if (kind === 5)
        return "erased";
      if (kind === 2)
        return "dynamic";
      if (kind === 3)
        return "void";
      if (kind === 1)
        return "Never";
      if (kind === 4)
        return "any";
      if (kind === 6)
        return A._rtiToString(rti._primary, genericContext);
      if (kind === 7) {
        questionArgument = rti._primary;
        s = A._rtiToString(questionArgument, genericContext);
        argumentKind = questionArgument._kind;
        return (argumentKind === 12 || argumentKind === 13 ? "(" + s + ")" : s) + "?";
      }
      if (kind === 8)
        return "FutureOr<" + A._rtiToString(rti._primary, genericContext) + ">";
      if (kind === 9) {
        $name = A._unminifyOrTag(rti._primary);
        $arguments = rti._rest;
        return $arguments.length > 0 ? $name + ("<" + A._rtiArrayToString($arguments, genericContext) + ">") : $name;
      }
      if (kind === 11)
        return A._recordRtiToString(rti, genericContext);
      if (kind === 12)
        return A._functionRtiToString(rti, genericContext, null);
      if (kind === 13)
        return A._functionRtiToString(rti._primary, genericContext, rti._rest);
      if (kind === 14) {
        t1 = rti._primary;
        t2 = genericContext.length;
        t1 = t2 - 1 - t1;
        if (!(t1 >= 0 && t1 < t2))
          return A.ioore(genericContext, t1);
        return genericContext[t1];
      }
      return "?";
    },
    _unminifyOrTag(rawClassName) {
      var preserved = init.mangledGlobalNames[rawClassName];
      if (preserved != null)
        return preserved;
      return rawClassName;
    },
    _Universe_findRule(universe, targetType) {
      var rule = universe.tR[targetType];
      for (; typeof rule == "string";)
        rule = universe.tR[rule];
      return rule;
    },
    _Universe_findErasedType(universe, cls) {
      var $length, erased, $arguments, i, $interface,
        t1 = universe.eT,
        probe = t1[cls];
      if (probe == null)
        return A._Universe_eval(universe, cls, false);
      else if (typeof probe == "number") {
        $length = probe;
        erased = A._Universe__lookupTerminalRti(universe, 5, "#");
        $arguments = A._Utils_newArrayOrEmpty($length);
        for (i = 0; i < $length; ++i)
          $arguments[i] = erased;
        $interface = A._Universe__lookupInterfaceRti(universe, cls, $arguments);
        t1[cls] = $interface;
        return $interface;
      } else
        return probe;
    },
    _Universe_addRules(universe, rules) {
      return A._Utils_objectAssign(universe.tR, rules);
    },
    _Universe_addErasedTypes(universe, types) {
      return A._Utils_objectAssign(universe.eT, types);
    },
    _Universe_eval(universe, recipe, normalize) {
      var rti,
        t1 = universe.eC,
        probe = t1.get(recipe);
      if (probe != null)
        return probe;
      rti = A._Parser_parse(A._Parser_create(universe, null, recipe, normalize));
      t1.set(recipe, rti);
      return rti;
    },
    _Universe_evalInEnvironment(universe, environment, recipe) {
      var probe, rti,
        cache = environment._evalCache;
      if (cache == null)
        cache = environment._evalCache = new Map();
      probe = cache.get(recipe);
      if (probe != null)
        return probe;
      rti = A._Parser_parse(A._Parser_create(universe, environment, recipe, true));
      cache.set(recipe, rti);
      return rti;
    },
    _Universe_bind(universe, environment, argumentsRti) {
      var argumentsRecipe, probe, rti,
        cache = environment._bindCache;
      if (cache == null)
        cache = environment._bindCache = new Map();
      argumentsRecipe = argumentsRti._canonicalRecipe;
      probe = cache.get(argumentsRecipe);
      if (probe != null)
        return probe;
      rti = A._Universe__lookupBindingRti(universe, environment, argumentsRti._kind === 10 ? argumentsRti._rest : [argumentsRti]);
      cache.set(argumentsRecipe, rti);
      return rti;
    },
    _Universe__installTypeTests(universe, rti) {
      rti._as = A._installSpecializedAsCheck;
      rti._is = A._installSpecializedIsTest;
      return rti;
    },
    _Universe__lookupTerminalRti(universe, kind, key) {
      var rti, t1,
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = kind;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupStarRti(universe, baseType, normalize) {
      var t1,
        key = baseType._canonicalRecipe + "*",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createStarRti(universe, baseType, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createStarRti(universe, baseType, key, normalize) {
      var baseKind, t1, rti;
      if (normalize) {
        baseKind = baseType._kind;
        if (!A.isSoundTopType(baseType))
          t1 = baseType === type$.Null || baseType === type$.JSNull || baseKind === 7 || baseKind === 6;
        else
          t1 = true;
        if (t1)
          return baseType;
      }
      rti = new A.Rti(null, null);
      rti._kind = 6;
      rti._primary = baseType;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Universe__lookupQuestionRti(universe, baseType, normalize) {
      var t1,
        key = baseType._canonicalRecipe + "?",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createQuestionRti(universe, baseType, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createQuestionRti(universe, baseType, key, normalize) {
      var baseKind, t1, starArgument, rti;
      if (normalize) {
        baseKind = baseType._kind;
        t1 = true;
        if (!A.isSoundTopType(baseType))
          if (!(baseType === type$.Null || baseType === type$.JSNull))
            if (baseKind !== 7)
              t1 = baseKind === 8 && A.isNullable(baseType._primary);
        if (t1)
          return baseType;
        else if (baseKind === 1 || baseType === type$.legacy_Never)
          return type$.Null;
        else if (baseKind === 6) {
          starArgument = baseType._primary;
          if (starArgument._kind === 8 && A.isNullable(starArgument._primary))
            return starArgument;
          else
            return A.Rti__getQuestionFromStar(universe, baseType);
        }
      }
      rti = new A.Rti(null, null);
      rti._kind = 7;
      rti._primary = baseType;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Universe__lookupFutureOrRti(universe, baseType, normalize) {
      var t1,
        key = baseType._canonicalRecipe + "/",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createFutureOrRti(universe, baseType, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createFutureOrRti(universe, baseType, key, normalize) {
      var t1, rti;
      if (normalize) {
        t1 = baseType._kind;
        if (A.isSoundTopType(baseType) || baseType === type$.Object || baseType === type$.legacy_Object)
          return baseType;
        else if (t1 === 1)
          return A._Universe__lookupInterfaceRti(universe, "Future", [baseType]);
        else if (baseType === type$.Null || baseType === type$.JSNull)
          return type$.nullable_Future_Null;
      }
      rti = new A.Rti(null, null);
      rti._kind = 8;
      rti._primary = baseType;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Universe__lookupGenericFunctionParameterRti(universe, index) {
      var rti, t1,
        key = "" + index + "^",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 14;
      rti._primary = index;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__canonicalRecipeJoin($arguments) {
      var s, sep, i,
        $length = $arguments.length;
      for (s = "", sep = "", i = 0; i < $length; ++i, sep = ",")
        s += sep + $arguments[i]._canonicalRecipe;
      return s;
    },
    _Universe__canonicalRecipeJoinNamed($arguments) {
      var s, sep, i, t1, nameSep,
        $length = $arguments.length;
      for (s = "", sep = "", i = 0; i < $length; i += 3, sep = ",") {
        t1 = $arguments[i];
        nameSep = $arguments[i + 1] ? "!" : ":";
        s += sep + t1 + nameSep + $arguments[i + 2]._canonicalRecipe;
      }
      return s;
    },
    _Universe__lookupInterfaceRti(universe, $name, $arguments) {
      var probe, rti, t1,
        s = $name;
      if ($arguments.length > 0)
        s += "<" + A._Universe__canonicalRecipeJoin($arguments) + ">";
      probe = universe.eC.get(s);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 9;
      rti._primary = $name;
      rti._rest = $arguments;
      if ($arguments.length > 0)
        rti._precomputed1 = $arguments[0];
      rti._canonicalRecipe = s;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(s, t1);
      return t1;
    },
    _Universe__lookupBindingRti(universe, base, $arguments) {
      var newBase, newArguments, key, probe, rti, t1;
      if (base._kind === 10) {
        newBase = base._primary;
        newArguments = base._rest.concat($arguments);
      } else {
        newArguments = $arguments;
        newBase = base;
      }
      key = newBase._canonicalRecipe + (";<" + A._Universe__canonicalRecipeJoin(newArguments) + ">");
      probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 10;
      rti._primary = newBase;
      rti._rest = newArguments;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupRecordRti(universe, partialShapeTag, fields) {
      var rti, t1,
        key = "+" + (partialShapeTag + "(" + A._Universe__canonicalRecipeJoin(fields) + ")"),
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 11;
      rti._primary = partialShapeTag;
      rti._rest = fields;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupFunctionRti(universe, returnType, parameters) {
      var sep, key, probe, rti, t1,
        s = returnType._canonicalRecipe,
        requiredPositional = parameters._requiredPositional,
        requiredPositionalLength = requiredPositional.length,
        optionalPositional = parameters._optionalPositional,
        optionalPositionalLength = optionalPositional.length,
        named = parameters._named,
        namedLength = named.length,
        recipe = "(" + A._Universe__canonicalRecipeJoin(requiredPositional);
      if (optionalPositionalLength > 0) {
        sep = requiredPositionalLength > 0 ? "," : "";
        recipe += sep + "[" + A._Universe__canonicalRecipeJoin(optionalPositional) + "]";
      }
      if (namedLength > 0) {
        sep = requiredPositionalLength > 0 ? "," : "";
        recipe += sep + "{" + A._Universe__canonicalRecipeJoinNamed(named) + "}";
      }
      key = s + (recipe + ")");
      probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 12;
      rti._primary = returnType;
      rti._rest = parameters;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupGenericFunctionRti(universe, baseFunctionType, bounds, normalize) {
      var t1,
        key = baseFunctionType._canonicalRecipe + ("<" + A._Universe__canonicalRecipeJoin(bounds) + ">"),
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createGenericFunctionRti(universe, baseFunctionType, bounds, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createGenericFunctionRti(universe, baseFunctionType, bounds, key, normalize) {
      var $length, typeArguments, count, i, bound, substitutedBase, substitutedBounds, rti;
      if (normalize) {
        $length = bounds.length;
        typeArguments = A._Utils_newArrayOrEmpty($length);
        for (count = 0, i = 0; i < $length; ++i) {
          bound = bounds[i];
          if (bound._kind === 1) {
            typeArguments[i] = bound;
            ++count;
          }
        }
        if (count > 0) {
          substitutedBase = A._substitute(universe, baseFunctionType, typeArguments, 0);
          substitutedBounds = A._substituteArray(universe, bounds, typeArguments, 0);
          return A._Universe__lookupGenericFunctionRti(universe, substitutedBase, substitutedBounds, bounds !== substitutedBounds);
        }
      }
      rti = new A.Rti(null, null);
      rti._kind = 13;
      rti._primary = baseFunctionType;
      rti._rest = bounds;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Parser_create(universe, environment, recipe, normalize) {
      return {u: universe, e: environment, r: recipe, s: [], p: 0, n: normalize};
    },
    _Parser_parse(parser) {
      var t2, i, ch, t3, array, end, item,
        source = parser.r,
        t1 = parser.s;
      for (t2 = source.length, i = 0; i < t2;) {
        ch = source.charCodeAt(i);
        if (ch >= 48 && ch <= 57)
          i = A._Parser_handleDigit(i + 1, ch, source, t1);
        else if ((((ch | 32) >>> 0) - 97 & 65535) < 26 || ch === 95 || ch === 36 || ch === 124)
          i = A._Parser_handleIdentifier(parser, i, source, t1, false);
        else if (ch === 46)
          i = A._Parser_handleIdentifier(parser, i, source, t1, true);
        else {
          ++i;
          switch (ch) {
            case 44:
              break;
            case 58:
              t1.push(false);
              break;
            case 33:
              t1.push(true);
              break;
            case 59:
              t1.push(A._Parser_toType(parser.u, parser.e, t1.pop()));
              break;
            case 94:
              t1.push(A._Universe__lookupGenericFunctionParameterRti(parser.u, t1.pop()));
              break;
            case 35:
              t1.push(A._Universe__lookupTerminalRti(parser.u, 5, "#"));
              break;
            case 64:
              t1.push(A._Universe__lookupTerminalRti(parser.u, 2, "@"));
              break;
            case 126:
              t1.push(A._Universe__lookupTerminalRti(parser.u, 3, "~"));
              break;
            case 60:
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 62:
              A._Parser_handleTypeArguments(parser, t1);
              break;
            case 38:
              A._Parser_handleExtendedOperations(parser, t1);
              break;
            case 42:
              t3 = parser.u;
              t1.push(A._Universe__lookupStarRti(t3, A._Parser_toType(t3, parser.e, t1.pop()), parser.n));
              break;
            case 63:
              t3 = parser.u;
              t1.push(A._Universe__lookupQuestionRti(t3, A._Parser_toType(t3, parser.e, t1.pop()), parser.n));
              break;
            case 47:
              t3 = parser.u;
              t1.push(A._Universe__lookupFutureOrRti(t3, A._Parser_toType(t3, parser.e, t1.pop()), parser.n));
              break;
            case 40:
              t1.push(-3);
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 41:
              A._Parser_handleArguments(parser, t1);
              break;
            case 91:
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 93:
              array = t1.splice(parser.p);
              A._Parser_toTypes(parser.u, parser.e, array);
              parser.p = t1.pop();
              t1.push(array);
              t1.push(-1);
              break;
            case 123:
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 125:
              array = t1.splice(parser.p);
              A._Parser_toTypesNamed(parser.u, parser.e, array);
              parser.p = t1.pop();
              t1.push(array);
              t1.push(-2);
              break;
            case 43:
              end = source.indexOf("(", i);
              t1.push(source.substring(i, end));
              t1.push(-4);
              t1.push(parser.p);
              parser.p = t1.length;
              i = end + 1;
              break;
            default:
              throw "Bad character " + ch;
          }
        }
      }
      item = t1.pop();
      return A._Parser_toType(parser.u, parser.e, item);
    },
    _Parser_handleDigit(i, digit, source, stack) {
      var t1, ch,
        value = digit - 48;
      for (t1 = source.length; i < t1; ++i) {
        ch = source.charCodeAt(i);
        if (!(ch >= 48 && ch <= 57))
          break;
        value = value * 10 + (ch - 48);
      }
      stack.push(value);
      return i;
    },
    _Parser_handleIdentifier(parser, start, source, stack, hasPeriod) {
      var t1, ch, t2, string, environment, recipe,
        i = start + 1;
      for (t1 = source.length; i < t1; ++i) {
        ch = source.charCodeAt(i);
        if (ch === 46) {
          if (hasPeriod)
            break;
          hasPeriod = true;
        } else {
          if (!((((ch | 32) >>> 0) - 97 & 65535) < 26 || ch === 95 || ch === 36 || ch === 124))
            t2 = ch >= 48 && ch <= 57;
          else
            t2 = true;
          if (!t2)
            break;
        }
      }
      string = source.substring(start, i);
      if (hasPeriod) {
        t1 = parser.u;
        environment = parser.e;
        if (environment._kind === 10)
          environment = environment._primary;
        recipe = A._Universe_findRule(t1, environment._primary)[string];
        if (recipe == null)
          A.throwExpression('No "' + string + '" in "' + A.Rti__getCanonicalRecipe(environment) + '"');
        stack.push(A._Universe_evalInEnvironment(t1, environment, recipe));
      } else
        stack.push(string);
      return i;
    },
    _Parser_handleTypeArguments(parser, stack) {
      var base,
        t1 = parser.u,
        $arguments = A._Parser_collectArray(parser, stack),
        head = stack.pop();
      if (typeof head == "string")
        stack.push(A._Universe__lookupInterfaceRti(t1, head, $arguments));
      else {
        base = A._Parser_toType(t1, parser.e, head);
        switch (base._kind) {
          case 12:
            stack.push(A._Universe__lookupGenericFunctionRti(t1, base, $arguments, parser.n));
            break;
          default:
            stack.push(A._Universe__lookupBindingRti(t1, base, $arguments));
            break;
        }
      }
    },
    _Parser_handleArguments(parser, stack) {
      var requiredPositional, returnType, parameters,
        t1 = parser.u,
        head = stack.pop(),
        optionalPositional = null, named = null;
      if (typeof head == "number")
        switch (head) {
          case -1:
            optionalPositional = stack.pop();
            break;
          case -2:
            named = stack.pop();
            break;
          default:
            stack.push(head);
            break;
        }
      else
        stack.push(head);
      requiredPositional = A._Parser_collectArray(parser, stack);
      head = stack.pop();
      switch (head) {
        case -3:
          head = stack.pop();
          if (optionalPositional == null)
            optionalPositional = t1.sEA;
          if (named == null)
            named = t1.sEA;
          returnType = A._Parser_toType(t1, parser.e, head);
          parameters = new A._FunctionParameters();
          parameters._requiredPositional = requiredPositional;
          parameters._optionalPositional = optionalPositional;
          parameters._named = named;
          stack.push(A._Universe__lookupFunctionRti(t1, returnType, parameters));
          return;
        case -4:
          stack.push(A._Universe__lookupRecordRti(t1, stack.pop(), requiredPositional));
          return;
        default:
          throw A.wrapException(A.AssertionError$("Unexpected state under `()`: " + A.S(head)));
      }
    },
    _Parser_handleExtendedOperations(parser, stack) {
      var $top = stack.pop();
      if (0 === $top) {
        stack.push(A._Universe__lookupTerminalRti(parser.u, 1, "0&"));
        return;
      }
      if (1 === $top) {
        stack.push(A._Universe__lookupTerminalRti(parser.u, 4, "1&"));
        return;
      }
      throw A.wrapException(A.AssertionError$("Unexpected extended operation " + A.S($top)));
    },
    _Parser_collectArray(parser, stack) {
      var array = stack.splice(parser.p);
      A._Parser_toTypes(parser.u, parser.e, array);
      parser.p = stack.pop();
      return array;
    },
    _Parser_toType(universe, environment, item) {
      if (typeof item == "string")
        return A._Universe__lookupInterfaceRti(universe, item, universe.sEA);
      else if (typeof item == "number") {
        environment.toString;
        return A._Parser_indexToType(universe, environment, item);
      } else
        return item;
    },
    _Parser_toTypes(universe, environment, items) {
      var i,
        $length = items.length;
      for (i = 0; i < $length; ++i)
        items[i] = A._Parser_toType(universe, environment, items[i]);
    },
    _Parser_toTypesNamed(universe, environment, items) {
      var i,
        $length = items.length;
      for (i = 2; i < $length; i += 3)
        items[i] = A._Parser_toType(universe, environment, items[i]);
    },
    _Parser_indexToType(universe, environment, index) {
      var typeArguments, len,
        kind = environment._kind;
      if (kind === 10) {
        if (index === 0)
          return environment._primary;
        typeArguments = environment._rest;
        len = typeArguments.length;
        if (index <= len)
          return typeArguments[index - 1];
        index -= len;
        environment = environment._primary;
        kind = environment._kind;
      } else if (index === 0)
        return environment;
      if (kind !== 9)
        throw A.wrapException(A.AssertionError$("Indexed base must be an interface type"));
      typeArguments = environment._rest;
      if (index <= typeArguments.length)
        return typeArguments[index - 1];
      throw A.wrapException(A.AssertionError$("Bad index " + index + " for " + environment.toString$0(0)));
    },
    isSubtype(universe, s, t) {
      var result,
        sCache = s._isSubtypeCache;
      if (sCache == null)
        sCache = s._isSubtypeCache = new Map();
      result = sCache.get(t);
      if (result == null) {
        result = A._isSubtype(universe, s, null, t, null, false) ? 1 : 0;
        sCache.set(t, result);
      }
      if (0 === result)
        return false;
      if (1 === result)
        return true;
      return true;
    },
    _isSubtype(universe, s, sEnv, t, tEnv, isLegacy) {
      var t1, sKind, leftTypeVariable, tKind, t2, sBounds, tBounds, sLength, i, sBound, tBound;
      if (s === t)
        return true;
      if (!A.isSoundTopType(t))
        t1 = t === type$.legacy_Object;
      else
        t1 = true;
      if (t1)
        return true;
      sKind = s._kind;
      if (sKind === 4)
        return true;
      if (A.isSoundTopType(s))
        return false;
      t1 = s._kind;
      if (t1 === 1)
        return true;
      leftTypeVariable = sKind === 14;
      if (leftTypeVariable)
        if (A._isSubtype(universe, sEnv[s._primary], sEnv, t, tEnv, false))
          return true;
      tKind = t._kind;
      t1 = s === type$.Null || s === type$.JSNull;
      if (t1) {
        if (tKind === 8)
          return A._isSubtype(universe, s, sEnv, t._primary, tEnv, false);
        return t === type$.Null || t === type$.JSNull || tKind === 7 || tKind === 6;
      }
      if (t === type$.Object) {
        if (sKind === 8)
          return A._isSubtype(universe, s._primary, sEnv, t, tEnv, false);
        if (sKind === 6)
          return A._isSubtype(universe, s._primary, sEnv, t, tEnv, false);
        return sKind !== 7;
      }
      if (sKind === 6)
        return A._isSubtype(universe, s._primary, sEnv, t, tEnv, false);
      if (tKind === 6) {
        t1 = A.Rti__getQuestionFromStar(universe, t);
        return A._isSubtype(universe, s, sEnv, t1, tEnv, false);
      }
      if (sKind === 8) {
        if (!A._isSubtype(universe, s._primary, sEnv, t, tEnv, false))
          return false;
        return A._isSubtype(universe, A.Rti__getFutureFromFutureOr(universe, s), sEnv, t, tEnv, false);
      }
      if (sKind === 7) {
        t1 = A._isSubtype(universe, type$.Null, sEnv, t, tEnv, false);
        return t1 && A._isSubtype(universe, s._primary, sEnv, t, tEnv, false);
      }
      if (tKind === 8) {
        if (A._isSubtype(universe, s, sEnv, t._primary, tEnv, false))
          return true;
        return A._isSubtype(universe, s, sEnv, A.Rti__getFutureFromFutureOr(universe, t), tEnv, false);
      }
      if (tKind === 7) {
        t1 = A._isSubtype(universe, s, sEnv, type$.Null, tEnv, false);
        return t1 || A._isSubtype(universe, s, sEnv, t._primary, tEnv, false);
      }
      if (leftTypeVariable)
        return false;
      t1 = sKind !== 12;
      if ((!t1 || sKind === 13) && t === type$.Function)
        return true;
      t2 = sKind === 11;
      if (t2 && t === type$.Record)
        return true;
      if (tKind === 13) {
        if (s === type$.JavaScriptFunction)
          return true;
        if (sKind !== 13)
          return false;
        sBounds = s._rest;
        tBounds = t._rest;
        sLength = sBounds.length;
        if (sLength !== tBounds.length)
          return false;
        sEnv = sEnv == null ? sBounds : sBounds.concat(sEnv);
        tEnv = tEnv == null ? tBounds : tBounds.concat(tEnv);
        for (i = 0; i < sLength; ++i) {
          sBound = sBounds[i];
          tBound = tBounds[i];
          if (!A._isSubtype(universe, sBound, sEnv, tBound, tEnv, false) || !A._isSubtype(universe, tBound, tEnv, sBound, sEnv, false))
            return false;
        }
        return A._isFunctionSubtype(universe, s._primary, sEnv, t._primary, tEnv, false);
      }
      if (tKind === 12) {
        if (s === type$.JavaScriptFunction)
          return true;
        if (t1)
          return false;
        return A._isFunctionSubtype(universe, s, sEnv, t, tEnv, false);
      }
      if (sKind === 9) {
        if (tKind !== 9)
          return false;
        return A._isInterfaceSubtype(universe, s, sEnv, t, tEnv, false);
      }
      if (t2 && tKind === 11)
        return A._isRecordSubtype(universe, s, sEnv, t, tEnv, false);
      return false;
    },
    _isFunctionSubtype(universe, s, sEnv, t, tEnv, isLegacy) {
      var sParameters, tParameters, sRequiredPositional, tRequiredPositional, sRequiredPositionalLength, tRequiredPositionalLength, requiredPositionalDelta, sOptionalPositional, tOptionalPositional, sOptionalPositionalLength, tOptionalPositionalLength, i, t1, sNamed, tNamed, sNamedLength, tNamedLength, sIndex, tIndex, tName, sName, sIsRequired;
      if (!A._isSubtype(universe, s._primary, sEnv, t._primary, tEnv, false))
        return false;
      sParameters = s._rest;
      tParameters = t._rest;
      sRequiredPositional = sParameters._requiredPositional;
      tRequiredPositional = tParameters._requiredPositional;
      sRequiredPositionalLength = sRequiredPositional.length;
      tRequiredPositionalLength = tRequiredPositional.length;
      if (sRequiredPositionalLength > tRequiredPositionalLength)
        return false;
      requiredPositionalDelta = tRequiredPositionalLength - sRequiredPositionalLength;
      sOptionalPositional = sParameters._optionalPositional;
      tOptionalPositional = tParameters._optionalPositional;
      sOptionalPositionalLength = sOptionalPositional.length;
      tOptionalPositionalLength = tOptionalPositional.length;
      if (sRequiredPositionalLength + sOptionalPositionalLength < tRequiredPositionalLength + tOptionalPositionalLength)
        return false;
      for (i = 0; i < sRequiredPositionalLength; ++i) {
        t1 = sRequiredPositional[i];
        if (!A._isSubtype(universe, tRequiredPositional[i], tEnv, t1, sEnv, false))
          return false;
      }
      for (i = 0; i < requiredPositionalDelta; ++i) {
        t1 = sOptionalPositional[i];
        if (!A._isSubtype(universe, tRequiredPositional[sRequiredPositionalLength + i], tEnv, t1, sEnv, false))
          return false;
      }
      for (i = 0; i < tOptionalPositionalLength; ++i) {
        t1 = sOptionalPositional[requiredPositionalDelta + i];
        if (!A._isSubtype(universe, tOptionalPositional[i], tEnv, t1, sEnv, false))
          return false;
      }
      sNamed = sParameters._named;
      tNamed = tParameters._named;
      sNamedLength = sNamed.length;
      tNamedLength = tNamed.length;
      for (sIndex = 0, tIndex = 0; tIndex < tNamedLength; tIndex += 3) {
        tName = tNamed[tIndex];
        for (; true;) {
          if (sIndex >= sNamedLength)
            return false;
          sName = sNamed[sIndex];
          sIndex += 3;
          if (tName < sName)
            return false;
          sIsRequired = sNamed[sIndex - 2];
          if (sName < tName) {
            if (sIsRequired)
              return false;
            continue;
          }
          t1 = tNamed[tIndex + 1];
          if (sIsRequired && !t1)
            return false;
          t1 = sNamed[sIndex - 1];
          if (!A._isSubtype(universe, tNamed[tIndex + 2], tEnv, t1, sEnv, false))
            return false;
          break;
        }
      }
      for (; sIndex < sNamedLength;) {
        if (sNamed[sIndex + 1])
          return false;
        sIndex += 3;
      }
      return true;
    },
    _isInterfaceSubtype(universe, s, sEnv, t, tEnv, isLegacy) {
      var rule, recipes, $length, supertypeArgs, i,
        sName = s._primary,
        tName = t._primary;
      for (; sName !== tName;) {
        rule = universe.tR[sName];
        if (rule == null)
          return false;
        if (typeof rule == "string") {
          sName = rule;
          continue;
        }
        recipes = rule[tName];
        if (recipes == null)
          return false;
        $length = recipes.length;
        supertypeArgs = $length > 0 ? new Array($length) : init.typeUniverse.sEA;
        for (i = 0; i < $length; ++i)
          supertypeArgs[i] = A._Universe_evalInEnvironment(universe, s, recipes[i]);
        return A._areArgumentsSubtypes(universe, supertypeArgs, null, sEnv, t._rest, tEnv, false);
      }
      return A._areArgumentsSubtypes(universe, s._rest, null, sEnv, t._rest, tEnv, false);
    },
    _areArgumentsSubtypes(universe, sArgs, sVariances, sEnv, tArgs, tEnv, isLegacy) {
      var i,
        $length = sArgs.length;
      for (i = 0; i < $length; ++i)
        if (!A._isSubtype(universe, sArgs[i], sEnv, tArgs[i], tEnv, false))
          return false;
      return true;
    },
    _isRecordSubtype(universe, s, sEnv, t, tEnv, isLegacy) {
      var i,
        sFields = s._rest,
        tFields = t._rest,
        sCount = sFields.length;
      if (sCount !== tFields.length)
        return false;
      if (s._primary !== t._primary)
        return false;
      for (i = 0; i < sCount; ++i)
        if (!A._isSubtype(universe, sFields[i], sEnv, tFields[i], tEnv, false))
          return false;
      return true;
    },
    isNullable(t) {
      var kind = t._kind,
        t1 = true;
      if (!(t === type$.Null || t === type$.JSNull))
        if (!A.isSoundTopType(t))
          if (kind !== 7)
            if (!(kind === 6 && A.isNullable(t._primary)))
              t1 = kind === 8 && A.isNullable(t._primary);
      return t1;
    },
    isDefinitelyTopType(t) {
      var t1;
      if (!A.isSoundTopType(t))
        t1 = t === type$.legacy_Object;
      else
        t1 = true;
      return t1;
    },
    isSoundTopType(t) {
      var kind = t._kind;
      return kind === 2 || kind === 3 || kind === 4 || kind === 5 || t === type$.nullable_Object;
    },
    _Utils_objectAssign(o, other) {
      var i, key,
        keys = Object.keys(other),
        $length = keys.length;
      for (i = 0; i < $length; ++i) {
        key = keys[i];
        o[key] = other[key];
      }
    },
    _Utils_newArrayOrEmpty($length) {
      return $length > 0 ? new Array($length) : init.typeUniverse.sEA;
    },
    Rti: function Rti(t0, t1) {
      var _ = this;
      _._as = t0;
      _._is = t1;
      _._cachedRuntimeType = _._specializedTestResource = _._isSubtypeCache = _._precomputed1 = null;
      _._kind = 0;
      _._canonicalRecipe = _._bindCache = _._evalCache = _._rest = _._primary = null;
    },
    _FunctionParameters: function _FunctionParameters() {
      this._named = this._optionalPositional = this._requiredPositional = null;
    },
    _Type: function _Type(t0) {
      this._rti = t0;
    },
    _Error: function _Error() {
    },
    _TypeError: function _TypeError(t0) {
      this.__rti$_message = t0;
    },
    _AsyncRun__initializeScheduleImmediate() {
      var t1, div, span;
      if (self.scheduleImmediate != null)
        return A.async__AsyncRun__scheduleImmediateJsOverride$closure();
      if (self.MutationObserver != null && self.document != null) {
        t1 = {};
        div = self.document.createElement("div");
        span = self.document.createElement("span");
        t1.storedCallback = null;
        new self.MutationObserver(A.convertDartClosureToJS(new A._AsyncRun__initializeScheduleImmediate_internalCallback(t1), 1)).observe(div, {childList: true});
        return new A._AsyncRun__initializeScheduleImmediate_closure(t1, div, span);
      } else if (self.setImmediate != null)
        return A.async__AsyncRun__scheduleImmediateWithSetImmediate$closure();
      return A.async__AsyncRun__scheduleImmediateWithTimer$closure();
    },
    _AsyncRun__scheduleImmediateJsOverride(callback) {
      self.scheduleImmediate(A.convertDartClosureToJS(new A._AsyncRun__scheduleImmediateJsOverride_internalCallback(type$.void_Function._as(callback)), 0));
    },
    _AsyncRun__scheduleImmediateWithSetImmediate(callback) {
      self.setImmediate(A.convertDartClosureToJS(new A._AsyncRun__scheduleImmediateWithSetImmediate_internalCallback(type$.void_Function._as(callback)), 0));
    },
    _AsyncRun__scheduleImmediateWithTimer(callback) {
      type$.void_Function._as(callback);
      A._TimerImpl$(0, callback);
    },
    _TimerImpl$(milliseconds, callback) {
      var t1 = new A._TimerImpl();
      t1._TimerImpl$2(milliseconds, callback);
      return t1;
    },
    _makeAsyncAwaitCompleter($T) {
      return new A._AsyncAwaitCompleter(new A._Future($.Zone__current, $T._eval$1("_Future<0>")), $T._eval$1("_AsyncAwaitCompleter<0>"));
    },
    _asyncStartSync(bodyFunction, completer) {
      bodyFunction.call$2(0, null);
      completer.isSync = true;
      return completer._future;
    },
    _asyncAwait(object, bodyFunction) {
      A._awaitOnObject(object, bodyFunction);
    },
    _asyncReturn(object, completer) {
      completer.complete$1(object);
    },
    _asyncRethrow(object, completer) {
      completer.completeError$2(A.unwrapException(object), A.getTraceFromException(object));
    },
    _awaitOnObject(object, bodyFunction) {
      var t1, future,
        thenCallback = new A._awaitOnObject_closure(bodyFunction),
        errorCallback = new A._awaitOnObject_closure0(bodyFunction);
      if (object instanceof A._Future)
        object._thenAwait$1$2(thenCallback, errorCallback, type$.dynamic);
      else {
        t1 = type$.dynamic;
        if (object instanceof A._Future)
          object.then$1$2$onError(thenCallback, errorCallback, t1);
        else {
          future = new A._Future($.Zone__current, type$._Future_dynamic);
          future._state = 8;
          future._resultOrListeners = object;
          future._thenAwait$1$2(thenCallback, errorCallback, t1);
        }
      }
    },
    _wrapJsFunctionForAsync($function) {
      var $protected = function(fn, ERROR) {
        return function(errorCode, result) {
          while (true) {
            try {
              fn(errorCode, result);
              break;
            } catch (error) {
              result = error;
              errorCode = ERROR;
            }
          }
        };
      }($function, 1);
      return $.Zone__current.registerBinaryCallback$3$1(new A._wrapJsFunctionForAsync_closure($protected), type$.void, type$.int, type$.dynamic);
    },
    AsyncError_defaultStackTrace(error) {
      var stackTrace;
      if (type$.Error._is(error)) {
        stackTrace = error.get$stackTrace();
        if (stackTrace != null)
          return stackTrace;
      }
      return B.C__StringStackTrace;
    },
    _interceptError(error, stackTrace) {
      if ($.Zone__current === B.C__RootZone)
        return null;
      return null;
    },
    _interceptUserError(error, stackTrace) {
      if ($.Zone__current !== B.C__RootZone)
        A._interceptError(error, stackTrace);
      if (stackTrace == null)
        if (type$.Error._is(error)) {
          stackTrace = error.get$stackTrace();
          if (stackTrace == null) {
            A.Primitives_trySetStackTrace(error, B.C__StringStackTrace);
            stackTrace = B.C__StringStackTrace;
          }
        } else
          stackTrace = B.C__StringStackTrace;
      else if (type$.Error._is(error))
        A.Primitives_trySetStackTrace(error, stackTrace);
      return new A.AsyncError(error, stackTrace);
    },
    _Future__chainCoreFuture(source, target, sync) {
      var t2, t3, ignoreError, listeners, _box_0 = {},
        t1 = _box_0.source = source;
      for (t2 = type$._Future_dynamic; t3 = t1._state, (t3 & 4) !== 0; t1 = source) {
        source = t2._as(t1._resultOrListeners);
        _box_0.source = source;
      }
      if (t1 === target) {
        target._asyncCompleteError$2(new A.ArgumentError(true, t1, null, "Cannot complete a future with itself"), A.StackTrace_current());
        return;
      }
      ignoreError = target._state & 1;
      t2 = t1._state = t3 | ignoreError;
      if ((t2 & 24) === 0) {
        listeners = type$.nullable__FutureListener_dynamic_dynamic._as(target._resultOrListeners);
        target._state = target._state & 1 | 4;
        target._resultOrListeners = t1;
        t1._prependListeners$1(listeners);
        return;
      }
      if (!sync)
        if (target._resultOrListeners == null)
          t1 = (t2 & 16) === 0 || ignoreError !== 0;
        else
          t1 = false;
      else
        t1 = true;
      if (t1) {
        listeners = target._removeListeners$0();
        target._cloneResult$1(_box_0.source);
        A._Future__propagateToListeners(target, listeners);
        return;
      }
      target._state ^= 2;
      A._rootScheduleMicrotask(null, null, target._zone, type$.void_Function._as(new A._Future__chainCoreFuture_closure(_box_0, target)));
    },
    _Future__propagateToListeners(source, listeners) {
      var t2, t3, t4, _box_0, t5, t6, hasError, asyncError, nextListener, nextListener0, sourceResult, t7, zone, oldZone, result, current, _box_1 = {},
        t1 = _box_1.source = source;
      for (t2 = type$.AsyncError, t3 = type$.nullable__FutureListener_dynamic_dynamic, t4 = type$.Future_dynamic; true;) {
        _box_0 = {};
        t5 = t1._state;
        t6 = (t5 & 16) === 0;
        hasError = !t6;
        if (listeners == null) {
          if (hasError && (t5 & 1) === 0) {
            asyncError = t2._as(t1._resultOrListeners);
            A._rootHandleError(asyncError.error, asyncError.stackTrace);
          }
          return;
        }
        _box_0.listener = listeners;
        nextListener = listeners._nextListener;
        for (t1 = listeners; nextListener != null; t1 = nextListener, nextListener = nextListener0) {
          t1._nextListener = null;
          A._Future__propagateToListeners(_box_1.source, t1);
          _box_0.listener = nextListener;
          nextListener0 = nextListener._nextListener;
        }
        t5 = _box_1.source;
        sourceResult = t5._resultOrListeners;
        _box_0.listenerHasError = hasError;
        _box_0.listenerValueOrError = sourceResult;
        if (t6) {
          t7 = t1.state;
          t7 = (t7 & 1) !== 0 || (t7 & 15) === 8;
        } else
          t7 = true;
        if (t7) {
          zone = t1.result._zone;
          if (hasError) {
            t5 = t5._zone === zone;
            t5 = !(t5 || t5);
          } else
            t5 = false;
          if (t5) {
            t2._as(sourceResult);
            A._rootHandleError(sourceResult.error, sourceResult.stackTrace);
            return;
          }
          oldZone = $.Zone__current;
          if (oldZone !== zone)
            $.Zone__current = zone;
          else
            oldZone = null;
          t1 = t1.state;
          if ((t1 & 15) === 8)
            new A._Future__propagateToListeners_handleWhenCompleteCallback(_box_0, _box_1, hasError).call$0();
          else if (t6) {
            if ((t1 & 1) !== 0)
              new A._Future__propagateToListeners_handleValueCallback(_box_0, sourceResult).call$0();
          } else if ((t1 & 2) !== 0)
            new A._Future__propagateToListeners_handleError(_box_1, _box_0).call$0();
          if (oldZone != null)
            $.Zone__current = oldZone;
          t1 = _box_0.listenerValueOrError;
          if (t1 instanceof A._Future) {
            t5 = _box_0.listener.$ti;
            t5 = t5._eval$1("Future<2>")._is(t1) || !t5._rest[1]._is(t1);
          } else
            t5 = false;
          if (t5) {
            t4._as(t1);
            result = _box_0.listener.result;
            if ((t1._state & 24) !== 0) {
              current = t3._as(result._resultOrListeners);
              result._resultOrListeners = null;
              listeners = result._reverseListeners$1(current);
              result._state = t1._state & 30 | result._state & 1;
              result._resultOrListeners = t1._resultOrListeners;
              _box_1.source = t1;
              continue;
            } else
              A._Future__chainCoreFuture(t1, result, true);
            return;
          }
        }
        result = _box_0.listener.result;
        current = t3._as(result._resultOrListeners);
        result._resultOrListeners = null;
        listeners = result._reverseListeners$1(current);
        t1 = _box_0.listenerHasError;
        t5 = _box_0.listenerValueOrError;
        if (!t1) {
          result.$ti._precomputed1._as(t5);
          result._state = 8;
          result._resultOrListeners = t5;
        } else {
          t2._as(t5);
          result._state = result._state & 1 | 16;
          result._resultOrListeners = t5;
        }
        _box_1.source = result;
        t1 = result;
      }
    },
    _registerErrorHandler(errorHandler, zone) {
      var t1;
      if (type$.dynamic_Function_Object_StackTrace._is(errorHandler))
        return zone.registerBinaryCallback$3$1(errorHandler, type$.dynamic, type$.Object, type$.StackTrace);
      t1 = type$.dynamic_Function_Object;
      if (t1._is(errorHandler))
        return t1._as(errorHandler);
      throw A.wrapException(A.ArgumentError$value(errorHandler, "onError", string$.Error_));
    },
    _microtaskLoop() {
      var entry, next;
      for (entry = $._nextCallback; entry != null; entry = $._nextCallback) {
        $._lastPriorityCallback = null;
        next = entry.next;
        $._nextCallback = next;
        if (next == null)
          $._lastCallback = null;
        entry.callback.call$0();
      }
    },
    _startMicrotaskLoop() {
      $._isInCallbackLoop = true;
      try {
        A._microtaskLoop();
      } finally {
        $._lastPriorityCallback = null;
        $._isInCallbackLoop = false;
        if ($._nextCallback != null)
          $.$get$_AsyncRun__scheduleImmediateClosure().call$1(A.async___startMicrotaskLoop$closure());
      }
    },
    _scheduleAsyncCallback(callback) {
      var newEntry = new A._AsyncCallbackEntry(callback),
        lastCallback = $._lastCallback;
      if (lastCallback == null) {
        $._nextCallback = $._lastCallback = newEntry;
        if (!$._isInCallbackLoop)
          $.$get$_AsyncRun__scheduleImmediateClosure().call$1(A.async___startMicrotaskLoop$closure());
      } else
        $._lastCallback = lastCallback.next = newEntry;
    },
    _schedulePriorityAsyncCallback(callback) {
      var entry, lastPriorityCallback, next,
        t1 = $._nextCallback;
      if (t1 == null) {
        A._scheduleAsyncCallback(callback);
        $._lastPriorityCallback = $._lastCallback;
        return;
      }
      entry = new A._AsyncCallbackEntry(callback);
      lastPriorityCallback = $._lastPriorityCallback;
      if (lastPriorityCallback == null) {
        entry.next = t1;
        $._nextCallback = $._lastPriorityCallback = entry;
      } else {
        next = lastPriorityCallback.next;
        entry.next = next;
        $._lastPriorityCallback = lastPriorityCallback.next = entry;
        if (next == null)
          $._lastCallback = entry;
      }
    },
    scheduleMicrotask(callback) {
      var _null = null,
        currentZone = $.Zone__current;
      if (B.C__RootZone === currentZone) {
        A._rootScheduleMicrotask(_null, _null, B.C__RootZone, callback);
        return;
      }
      A._rootScheduleMicrotask(_null, _null, currentZone, type$.void_Function._as(currentZone.bindCallbackGuarded$1(callback)));
    },
    StreamIterator_StreamIterator(stream, $T) {
      A.checkNotNullable(stream, "stream", type$.Object);
      return new A._StreamIterator($T._eval$1("_StreamIterator<0>"));
    },
    _runGuarded(notificationHandler) {
      return;
    },
    _BufferingStreamSubscription__registerErrorHandler(zone, handleError) {
      if (handleError == null)
        handleError = A.async___nullErrorHandler$closure();
      if (type$.void_Function_Object_StackTrace._is(handleError))
        return zone.registerBinaryCallback$3$1(handleError, type$.dynamic, type$.Object, type$.StackTrace);
      if (type$.void_Function_Object._is(handleError))
        return type$.dynamic_Function_Object._as(handleError);
      throw A.wrapException(A.ArgumentError$("handleError callback must take either an Object (the error), or both an Object (the error) and a StackTrace.", null));
    },
    _nullErrorHandler(error, stackTrace) {
      A._rootHandleError(error, stackTrace);
    },
    _nullDoneHandler() {
    },
    _rootHandleError(error, stackTrace) {
      A._schedulePriorityAsyncCallback(new A._rootHandleError_closure(error, stackTrace));
    },
    _rootRun($self, $parent, zone, f, $R) {
      var old,
        t1 = $.Zone__current;
      if (t1 === zone)
        return f.call$0();
      $.Zone__current = zone;
      old = t1;
      try {
        t1 = f.call$0();
        return t1;
      } finally {
        $.Zone__current = old;
      }
    },
    _rootRunUnary($self, $parent, zone, f, arg, $R, $T) {
      var old,
        t1 = $.Zone__current;
      if (t1 === zone)
        return f.call$1(arg);
      $.Zone__current = zone;
      old = t1;
      try {
        t1 = f.call$1(arg);
        return t1;
      } finally {
        $.Zone__current = old;
      }
    },
    _rootRunBinary($self, $parent, zone, f, arg1, arg2, $R, T1, T2) {
      var old,
        t1 = $.Zone__current;
      if (t1 === zone)
        return f.call$2(arg1, arg2);
      $.Zone__current = zone;
      old = t1;
      try {
        t1 = f.call$2(arg1, arg2);
        return t1;
      } finally {
        $.Zone__current = old;
      }
    },
    _rootScheduleMicrotask($self, $parent, zone, f) {
      type$.void_Function._as(f);
      if (B.C__RootZone !== zone)
        f = zone.bindCallbackGuarded$1(f);
      A._scheduleAsyncCallback(f);
    },
    _AsyncRun__initializeScheduleImmediate_internalCallback: function _AsyncRun__initializeScheduleImmediate_internalCallback(t0) {
      this._box_0 = t0;
    },
    _AsyncRun__initializeScheduleImmediate_closure: function _AsyncRun__initializeScheduleImmediate_closure(t0, t1, t2) {
      this._box_0 = t0;
      this.div = t1;
      this.span = t2;
    },
    _AsyncRun__scheduleImmediateJsOverride_internalCallback: function _AsyncRun__scheduleImmediateJsOverride_internalCallback(t0) {
      this.callback = t0;
    },
    _AsyncRun__scheduleImmediateWithSetImmediate_internalCallback: function _AsyncRun__scheduleImmediateWithSetImmediate_internalCallback(t0) {
      this.callback = t0;
    },
    _TimerImpl: function _TimerImpl() {
    },
    _TimerImpl_internalCallback: function _TimerImpl_internalCallback(t0, t1) {
      this.$this = t0;
      this.callback = t1;
    },
    _AsyncAwaitCompleter: function _AsyncAwaitCompleter(t0, t1) {
      this._future = t0;
      this.isSync = false;
      this.$ti = t1;
    },
    _awaitOnObject_closure: function _awaitOnObject_closure(t0) {
      this.bodyFunction = t0;
    },
    _awaitOnObject_closure0: function _awaitOnObject_closure0(t0) {
      this.bodyFunction = t0;
    },
    _wrapJsFunctionForAsync_closure: function _wrapJsFunctionForAsync_closure(t0) {
      this.$protected = t0;
    },
    AsyncError: function AsyncError(t0, t1) {
      this.error = t0;
      this.stackTrace = t1;
    },
    _BroadcastStream: function _BroadcastStream(t0, t1) {
      this._async$_controller = t0;
      this.$ti = t1;
    },
    _BroadcastSubscription: function _BroadcastSubscription(t0, t1, t2, t3, t4) {
      var _ = this;
      _._eventState = 0;
      _._async$_previous = _._async$_next = null;
      _._async$_controller = t0;
      _._onData = t1;
      _._zone = t2;
      _._state = t3;
      _._pending = null;
      _.$ti = t4;
    },
    _BroadcastStreamController: function _BroadcastStreamController() {
    },
    _SyncBroadcastStreamController: function _SyncBroadcastStreamController(t0, t1, t2) {
      var _ = this;
      _.onListen = t0;
      _.onCancel = t1;
      _._state = 0;
      _._lastSubscription = _._firstSubscription = null;
      _.$ti = t2;
    },
    _SyncBroadcastStreamController__sendData_closure: function _SyncBroadcastStreamController__sendData_closure(t0, t1) {
      this.$this = t0;
      this.data = t1;
    },
    _Completer: function _Completer() {
    },
    _AsyncCompleter: function _AsyncCompleter(t0, t1) {
      this.future = t0;
      this.$ti = t1;
    },
    _FutureListener: function _FutureListener(t0, t1, t2, t3, t4) {
      var _ = this;
      _._nextListener = null;
      _.result = t0;
      _.state = t1;
      _.callback = t2;
      _.errorCallback = t3;
      _.$ti = t4;
    },
    _Future: function _Future(t0, t1) {
      var _ = this;
      _._state = 0;
      _._zone = t0;
      _._resultOrListeners = null;
      _.$ti = t1;
    },
    _Future__addListener_closure: function _Future__addListener_closure(t0, t1) {
      this.$this = t0;
      this.listener = t1;
    },
    _Future__prependListeners_closure: function _Future__prependListeners_closure(t0, t1) {
      this._box_0 = t0;
      this.$this = t1;
    },
    _Future__chainForeignFuture_closure: function _Future__chainForeignFuture_closure(t0) {
      this.$this = t0;
    },
    _Future__chainForeignFuture_closure0: function _Future__chainForeignFuture_closure0(t0) {
      this.$this = t0;
    },
    _Future__chainForeignFuture_closure1: function _Future__chainForeignFuture_closure1(t0, t1, t2) {
      this.$this = t0;
      this.e = t1;
      this.s = t2;
    },
    _Future__chainCoreFuture_closure: function _Future__chainCoreFuture_closure(t0, t1) {
      this._box_0 = t0;
      this.target = t1;
    },
    _Future__asyncCompleteWithValue_closure: function _Future__asyncCompleteWithValue_closure(t0, t1) {
      this.$this = t0;
      this.value = t1;
    },
    _Future__asyncCompleteError_closure: function _Future__asyncCompleteError_closure(t0, t1, t2) {
      this.$this = t0;
      this.error = t1;
      this.stackTrace = t2;
    },
    _Future__propagateToListeners_handleWhenCompleteCallback: function _Future__propagateToListeners_handleWhenCompleteCallback(t0, t1, t2) {
      this._box_0 = t0;
      this._box_1 = t1;
      this.hasError = t2;
    },
    _Future__propagateToListeners_handleWhenCompleteCallback_closure: function _Future__propagateToListeners_handleWhenCompleteCallback_closure(t0, t1) {
      this.joinedResult = t0;
      this.originalSource = t1;
    },
    _Future__propagateToListeners_handleWhenCompleteCallback_closure0: function _Future__propagateToListeners_handleWhenCompleteCallback_closure0(t0) {
      this.joinedResult = t0;
    },
    _Future__propagateToListeners_handleValueCallback: function _Future__propagateToListeners_handleValueCallback(t0, t1) {
      this._box_0 = t0;
      this.sourceResult = t1;
    },
    _Future__propagateToListeners_handleError: function _Future__propagateToListeners_handleError(t0, t1) {
      this._box_1 = t0;
      this._box_0 = t1;
    },
    _AsyncCallbackEntry: function _AsyncCallbackEntry(t0) {
      this.callback = t0;
      this.next = null;
    },
    Stream: function Stream() {
    },
    Stream_length_closure: function Stream_length_closure(t0, t1) {
      this._box_0 = t0;
      this.$this = t1;
    },
    Stream_length_closure0: function Stream_length_closure0(t0, t1) {
      this._box_0 = t0;
      this.future = t1;
    },
    _ControllerStream: function _ControllerStream() {
    },
    _ControllerSubscription: function _ControllerSubscription() {
    },
    _BufferingStreamSubscription: function _BufferingStreamSubscription() {
    },
    _StreamImpl: function _StreamImpl() {
    },
    _DelayedEvent: function _DelayedEvent() {
    },
    _DelayedData: function _DelayedData(t0, t1) {
      this.value = t0;
      this.next = null;
      this.$ti = t1;
    },
    _PendingEvents: function _PendingEvents(t0) {
      var _ = this;
      _._state = 0;
      _.lastPendingEvent = _.firstPendingEvent = null;
      _.$ti = t0;
    },
    _PendingEvents_schedule_closure: function _PendingEvents_schedule_closure(t0, t1) {
      this.$this = t0;
      this.dispatch = t1;
    },
    _DoneStreamSubscription: function _DoneStreamSubscription(t0, t1) {
      var _ = this;
      _._state = 1;
      _._zone = t0;
      _._onDone = null;
      _.$ti = t1;
    },
    _StreamIterator: function _StreamIterator(t0) {
      this.$ti = t0;
    },
    _Zone: function _Zone() {
    },
    _rootHandleError_closure: function _rootHandleError_closure(t0, t1) {
      this.error = t0;
      this.stackTrace = t1;
    },
    _RootZone: function _RootZone() {
    },
    _RootZone_bindCallbackGuarded_closure: function _RootZone_bindCallbackGuarded_closure(t0, t1) {
      this.$this = t0;
      this.f = t1;
    },
    _HashMap__getTableEntry(table, key) {
      var entry = table[key];
      return entry === table ? null : entry;
    },
    _HashMap__setTableEntry(table, key, value) {
      if (value == null)
        table[key] = table;
      else
        table[key] = value;
    },
    _HashMap__newHashTable() {
      var table = Object.create(null);
      A._HashMap__setTableEntry(table, "<non-identifier-key>", table);
      delete table["<non-identifier-key>"];
      return table;
    },
    LinkedHashMap_LinkedHashMap$_literal(keyValuePairs, $K, $V) {
      return $K._eval$1("@<0>")._bind$1($V)._eval$1("LinkedHashMap<1,2>")._as(A.fillLiteralMap(keyValuePairs, new A.JsLinkedHashMap($K._eval$1("@<0>")._bind$1($V)._eval$1("JsLinkedHashMap<1,2>"))));
    },
    LinkedHashMap_LinkedHashMap$_empty($K, $V) {
      return new A.JsLinkedHashMap($K._eval$1("@<0>")._bind$1($V)._eval$1("JsLinkedHashMap<1,2>"));
    },
    MapBase_mapToString(m) {
      var result, t1;
      if (A.isToStringVisiting(m))
        return "{...}";
      result = new A.StringBuffer("");
      try {
        t1 = {};
        B.JSArray_methods.add$1($.toStringVisiting, m);
        result._contents += "{";
        t1.first = true;
        m.forEach$1(0, new A.MapBase_mapToString_closure(t1, result));
        result._contents += "}";
      } finally {
        if (0 >= $.toStringVisiting.length)
          return A.ioore($.toStringVisiting, -1);
        $.toStringVisiting.pop();
      }
      t1 = result._contents;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    },
    _HashMap: function _HashMap() {
    },
    _IdentityHashMap: function _IdentityHashMap(t0) {
      var _ = this;
      _._collection$_length = 0;
      _._collection$_keys = _._collection$_rest = _._collection$_nums = _._collection$_strings = null;
      _.$ti = t0;
    },
    _HashMapKeyIterable: function _HashMapKeyIterable(t0, t1) {
      this._collection$_map = t0;
      this.$ti = t1;
    },
    _HashMapKeyIterator: function _HashMapKeyIterator(t0, t1, t2) {
      var _ = this;
      _._collection$_map = t0;
      _._collection$_keys = t1;
      _._offset = 0;
      _._collection$_current = null;
      _.$ti = t2;
    },
    ListBase: function ListBase() {
    },
    MapBase: function MapBase() {
    },
    MapBase_mapToString_closure: function MapBase_mapToString_closure(t0, t1) {
      this._box_0 = t0;
      this.result = t1;
    },
    _UnmodifiableMapMixin: function _UnmodifiableMapMixin() {
    },
    MapView: function MapView() {
    },
    UnmodifiableMapView: function UnmodifiableMapView() {
    },
    _UnmodifiableMapView_MapView__UnmodifiableMapMixin: function _UnmodifiableMapView_MapView__UnmodifiableMapMixin() {
    },
    _Base64Encoder_encodeChunk(alphabet, bytes, start, end, isLast, output, outputIndex, state) {
      var t1, t2, t3, i, byteOr, byte, outputIndex0, t4, t5, outputIndex1,
        bits = state >>> 2,
        expectedChars = 3 - (state & 3);
      for (t1 = bytes.length, t2 = alphabet.length, t3 = output.$flags | 0, i = start, byteOr = 0; i < end; ++i) {
        if (!(i < t1))
          return A.ioore(bytes, i);
        byte = bytes[i];
        byteOr |= byte;
        bits = (bits << 8 | byte) & 16777215;
        --expectedChars;
        if (expectedChars === 0) {
          outputIndex0 = outputIndex + 1;
          t4 = bits >>> 18 & 63;
          if (!(t4 < t2))
            return A.ioore(alphabet, t4);
          t3 & 2 && A.throwUnsupportedOperation(output);
          t5 = output.length;
          if (!(outputIndex < t5))
            return A.ioore(output, outputIndex);
          output[outputIndex] = alphabet.charCodeAt(t4);
          outputIndex = outputIndex0 + 1;
          t4 = bits >>> 12 & 63;
          if (!(t4 < t2))
            return A.ioore(alphabet, t4);
          if (!(outputIndex0 < t5))
            return A.ioore(output, outputIndex0);
          output[outputIndex0] = alphabet.charCodeAt(t4);
          outputIndex0 = outputIndex + 1;
          t4 = bits >>> 6 & 63;
          if (!(t4 < t2))
            return A.ioore(alphabet, t4);
          if (!(outputIndex < t5))
            return A.ioore(output, outputIndex);
          output[outputIndex] = alphabet.charCodeAt(t4);
          outputIndex = outputIndex0 + 1;
          t4 = bits & 63;
          if (!(t4 < t2))
            return A.ioore(alphabet, t4);
          if (!(outputIndex0 < t5))
            return A.ioore(output, outputIndex0);
          output[outputIndex0] = alphabet.charCodeAt(t4);
          bits = 0;
          expectedChars = 3;
        }
      }
      if (byteOr >= 0 && byteOr <= 255) {
        if (expectedChars < 3) {
          outputIndex0 = outputIndex + 1;
          outputIndex1 = outputIndex0 + 1;
          if (3 - expectedChars === 1) {
            t1 = bits >>> 2 & 63;
            if (!(t1 < t2))
              return A.ioore(alphabet, t1);
            t3 & 2 && A.throwUnsupportedOperation(output);
            t3 = output.length;
            if (!(outputIndex < t3))
              return A.ioore(output, outputIndex);
            output[outputIndex] = alphabet.charCodeAt(t1);
            t1 = bits << 4 & 63;
            if (!(t1 < t2))
              return A.ioore(alphabet, t1);
            if (!(outputIndex0 < t3))
              return A.ioore(output, outputIndex0);
            output[outputIndex0] = alphabet.charCodeAt(t1);
            outputIndex = outputIndex1 + 1;
            if (!(outputIndex1 < t3))
              return A.ioore(output, outputIndex1);
            output[outputIndex1] = 61;
            if (!(outputIndex < t3))
              return A.ioore(output, outputIndex);
            output[outputIndex] = 61;
          } else {
            t1 = bits >>> 10 & 63;
            if (!(t1 < t2))
              return A.ioore(alphabet, t1);
            t3 & 2 && A.throwUnsupportedOperation(output);
            t3 = output.length;
            if (!(outputIndex < t3))
              return A.ioore(output, outputIndex);
            output[outputIndex] = alphabet.charCodeAt(t1);
            t1 = bits >>> 4 & 63;
            if (!(t1 < t2))
              return A.ioore(alphabet, t1);
            if (!(outputIndex0 < t3))
              return A.ioore(output, outputIndex0);
            output[outputIndex0] = alphabet.charCodeAt(t1);
            outputIndex = outputIndex1 + 1;
            t1 = bits << 2 & 63;
            if (!(t1 < t2))
              return A.ioore(alphabet, t1);
            if (!(outputIndex1 < t3))
              return A.ioore(output, outputIndex1);
            output[outputIndex1] = alphabet.charCodeAt(t1);
            if (!(outputIndex < t3))
              return A.ioore(output, outputIndex);
            output[outputIndex] = 61;
          }
          return 0;
        }
        return (bits << 2 | 3 - expectedChars) >>> 0;
      }
      for (i = start; i < end;) {
        if (!(i < t1))
          return A.ioore(bytes, i);
        byte = bytes[i];
        if (byte > 255)
          break;
        ++i;
      }
      if (!(i < t1))
        return A.ioore(bytes, i);
      throw A.wrapException(A.ArgumentError$value(bytes, "Not a byte value at index " + i + ": 0x" + B.JSInt_methods.toRadixString$1(bytes[i], 16), null));
    },
    _Base64Decoder_decodeChunk(input, start, end, output, outIndex, state) {
      var t1, t2, t3, i, charOr, char, t4, code, outIndex0, expectedPadding,
        _s31_ = "Invalid encoding before padding",
        _s17_ = "Invalid character",
        bits = B.JSInt_methods._shrOtherPositive$1(state, 2),
        count = state & 3,
        inverseAlphabet = $.$get$_Base64Decoder__inverseAlphabet();
      for (t1 = input.length, t2 = inverseAlphabet.length, t3 = output.$flags | 0, i = start, charOr = 0; i < end; ++i) {
        if (!(i < t1))
          return A.ioore(input, i);
        char = input.charCodeAt(i);
        charOr |= char;
        t4 = char & 127;
        if (!(t4 < t2))
          return A.ioore(inverseAlphabet, t4);
        code = inverseAlphabet[t4];
        if (code >= 0) {
          bits = (bits << 6 | code) & 16777215;
          count = count + 1 & 3;
          if (count === 0) {
            outIndex0 = outIndex + 1;
            t3 & 2 && A.throwUnsupportedOperation(output);
            t4 = output.length;
            if (!(outIndex < t4))
              return A.ioore(output, outIndex);
            output[outIndex] = bits >>> 16 & 255;
            outIndex = outIndex0 + 1;
            if (!(outIndex0 < t4))
              return A.ioore(output, outIndex0);
            output[outIndex0] = bits >>> 8 & 255;
            outIndex0 = outIndex + 1;
            if (!(outIndex < t4))
              return A.ioore(output, outIndex);
            output[outIndex] = bits & 255;
            outIndex = outIndex0;
            bits = 0;
          }
          continue;
        } else if (code === -1 && count > 1) {
          if (charOr > 127)
            break;
          if (count === 3) {
            if ((bits & 3) !== 0)
              throw A.wrapException(A.FormatException$(_s31_, input, i));
            outIndex0 = outIndex + 1;
            t3 & 2 && A.throwUnsupportedOperation(output);
            t1 = output.length;
            if (!(outIndex < t1))
              return A.ioore(output, outIndex);
            output[outIndex] = bits >>> 10;
            if (!(outIndex0 < t1))
              return A.ioore(output, outIndex0);
            output[outIndex0] = bits >>> 2;
          } else {
            if ((bits & 15) !== 0)
              throw A.wrapException(A.FormatException$(_s31_, input, i));
            t3 & 2 && A.throwUnsupportedOperation(output);
            if (!(outIndex < output.length))
              return A.ioore(output, outIndex);
            output[outIndex] = bits >>> 4;
          }
          expectedPadding = (3 - count) * 3;
          if (char === 37)
            expectedPadding += 2;
          return A._Base64Decoder__checkPadding(input, i + 1, end, -expectedPadding - 1);
        }
        throw A.wrapException(A.FormatException$(_s17_, input, i));
      }
      if (charOr >= 0 && charOr <= 127)
        return (bits << 2 | count) >>> 0;
      for (i = start; i < end; ++i) {
        if (!(i < t1))
          return A.ioore(input, i);
        if (input.charCodeAt(i) > 127)
          break;
      }
      throw A.wrapException(A.FormatException$(_s17_, input, i));
    },
    _Base64Decoder__allocateBuffer(input, start, end, state) {
      var paddingStart = A._Base64Decoder__trimPaddingChars(input, start, end),
        $length = (state & 3) + (paddingStart - start),
        bufferLength = B.JSInt_methods._shrOtherPositive$1($length, 2) * 3,
        remainderLength = $length & 3;
      if (remainderLength !== 0 && paddingStart < end)
        bufferLength += remainderLength - 1;
      if (bufferLength > 0)
        return new Uint8Array(bufferLength);
      return $.$get$_Base64Decoder__emptyBuffer();
    },
    _Base64Decoder__trimPaddingChars(input, start, end) {
      var char,
        t1 = input.length,
        newEnd = end,
        index = newEnd,
        padding = 0;
      while (true) {
        if (!(index > start && padding < 2))
          break;
        c$0: {
          --index;
          if (!(index >= 0 && index < t1))
            return A.ioore(input, index);
          char = input.charCodeAt(index);
          if (char === 61) {
            ++padding;
            newEnd = index;
            break c$0;
          }
          if ((char | 32) === 100) {
            if (index === start)
              break;
            --index;
            if (!(index >= 0 && index < t1))
              return A.ioore(input, index);
            char = input.charCodeAt(index);
          }
          if (char === 51) {
            if (index === start)
              break;
            --index;
            if (!(index >= 0 && index < t1))
              return A.ioore(input, index);
            char = input.charCodeAt(index);
          }
          if (char === 37) {
            ++padding;
            newEnd = index;
            break c$0;
          }
          break;
        }
      }
      return newEnd;
    },
    _Base64Decoder__checkPadding(input, start, end, state) {
      var expectedPadding, t1, char;
      if (start === end)
        return state;
      expectedPadding = -state - 1;
      for (t1 = input.length; expectedPadding > 0;) {
        if (!(start < t1))
          return A.ioore(input, start);
        char = input.charCodeAt(start);
        if (expectedPadding === 3) {
          if (char === 61) {
            expectedPadding -= 3;
            ++start;
            break;
          }
          if (char === 37) {
            --expectedPadding;
            ++start;
            if (start === end)
              break;
            if (!(start < t1))
              return A.ioore(input, start);
            char = input.charCodeAt(start);
          } else
            break;
        }
        if ((expectedPadding > 3 ? expectedPadding - 3 : expectedPadding) === 2) {
          if (char !== 51)
            break;
          ++start;
          --expectedPadding;
          if (start === end)
            break;
          if (!(start < t1))
            return A.ioore(input, start);
          char = input.charCodeAt(start);
        }
        if ((char | 32) !== 100)
          break;
        ++start;
        --expectedPadding;
        if (start === end)
          break;
      }
      if (start !== end)
        throw A.wrapException(A.FormatException$("Invalid padding character", input, start));
      return -expectedPadding - 1;
    },
    Base64Codec: function Base64Codec() {
    },
    Base64Encoder: function Base64Encoder() {
    },
    _Base64Encoder: function _Base64Encoder(t0) {
      this._convert$_state = 0;
      this._alphabet = t0;
    },
    Base64Decoder: function Base64Decoder() {
    },
    _Base64Decoder: function _Base64Decoder() {
      this._convert$_state = 0;
    },
    Codec: function Codec() {
    },
    Converter: function Converter() {
    },
    Error__throw(error, stackTrace) {
      error = A.wrapException(error);
      if (error == null)
        error = type$.Object._as(error);
      error.stack = stackTrace.toString$0(0);
      throw error;
      throw A.wrapException("unreachable");
    },
    List_List$filled($length, fill, growable, $E) {
      var i,
        result = J.JSArray_JSArray$fixed($length, $E);
      if ($length !== 0 && fill != null)
        for (i = 0; i < $length; ++i)
          result[i] = fill;
      return result;
    },
    List_List$of(elements, growable, $E) {
      var t1 = A.List_List$_of(elements, $E);
      return t1;
    },
    List_List$_of(elements, $E) {
      var list, t1;
      if (Array.isArray(elements))
        return A._setArrayType(elements.slice(0), $E._eval$1("JSArray<0>"));
      list = A._setArrayType([], $E._eval$1("JSArray<0>"));
      for (t1 = J.get$iterator$ax(elements); t1.moveNext$0();)
        B.JSArray_methods.add$1(list, t1.get$current());
      return list;
    },
    String_String$fromCharCodes(charCodes) {
      var t1;
      A.RangeError_checkNotNegative(0, "start");
      t1 = A.String__stringFromUint8List(charCodes, 0, null);
      return t1;
    },
    String__stringFromUint8List(charCodes, start, endOrNull) {
      var len = charCodes.length;
      if (start >= len)
        return "";
      return A.Primitives_stringFromNativeUint8List(charCodes, start, len);
    },
    StringBuffer__writeAll(string, objects, separator) {
      var iterator = J.get$iterator$ax(objects);
      if (!iterator.moveNext$0())
        return string;
      if (separator.length === 0) {
        do
          string += A.S(iterator.get$current());
        while (iterator.moveNext$0());
      } else {
        string += A.S(iterator.get$current());
        for (; iterator.moveNext$0();)
          string = string + separator + A.S(iterator.get$current());
      }
      return string;
    },
    NoSuchMethodError_NoSuchMethodError$withInvocation(receiver, invocation) {
      return new A.NoSuchMethodError(receiver, invocation.get$memberName(), invocation.get$positionalArguments(), invocation.get$namedArguments());
    },
    StackTrace_current() {
      return A.getTraceFromException(new Error());
    },
    DateTime__fourDigits(n) {
      var absN = Math.abs(n),
        sign = n < 0 ? "-" : "";
      if (absN >= 1000)
        return "" + n;
      if (absN >= 100)
        return sign + "0" + absN;
      if (absN >= 10)
        return sign + "00" + absN;
      return sign + "000" + absN;
    },
    DateTime__threeDigits(n) {
      if (n >= 100)
        return "" + n;
      if (n >= 10)
        return "0" + n;
      return "00" + n;
    },
    DateTime__twoDigits(n) {
      if (n >= 10)
        return "" + n;
      return "0" + n;
    },
    Error_safeToString(object) {
      if (typeof object == "number" || A._isBool(object) || object == null)
        return J.toString$0$(object);
      if (typeof object == "string")
        return JSON.stringify(object);
      return A.Primitives_safeToString(object);
    },
    Error_throwWithStackTrace(error, stackTrace) {
      A.checkNotNullable(error, "error", type$.Object);
      A.checkNotNullable(stackTrace, "stackTrace", type$.StackTrace);
      A.Error__throw(error, stackTrace);
    },
    AssertionError$(message) {
      return new A.AssertionError(message);
    },
    ArgumentError$(message, $name) {
      return new A.ArgumentError(false, null, $name, message);
    },
    ArgumentError$value(value, $name, message) {
      return new A.ArgumentError(true, value, $name, message);
    },
    RangeError$value(value, $name) {
      return new A.RangeError(null, null, true, value, $name, "Value not in range");
    },
    RangeError$range(invalidValue, minValue, maxValue, $name, message) {
      return new A.RangeError(minValue, maxValue, true, invalidValue, $name, "Invalid value");
    },
    RangeError_checkValidRange(start, end, $length) {
      if (0 > start || start > $length)
        throw A.wrapException(A.RangeError$range(start, 0, $length, "start", null));
      if (end != null) {
        if (start > end || end > $length)
          throw A.wrapException(A.RangeError$range(end, start, $length, "end", null));
        return end;
      }
      return $length;
    },
    RangeError_checkNotNegative(value, $name) {
      if (value < 0)
        throw A.wrapException(A.RangeError$range(value, 0, null, $name, null));
      return value;
    },
    IndexError$withLength(invalidValue, $length, indexable, $name) {
      return new A.IndexError($length, true, invalidValue, $name, "Index out of range");
    },
    UnsupportedError$(message) {
      return new A.UnsupportedError(message);
    },
    UnimplementedError$(message) {
      return new A.UnimplementedError(message);
    },
    StateError$(message) {
      return new A.StateError(message);
    },
    ConcurrentModificationError$(modifiedObject) {
      return new A.ConcurrentModificationError(modifiedObject);
    },
    Exception_Exception(message) {
      return new A._Exception(message);
    },
    FormatException$(message, source, offset) {
      return new A.FormatException(message, source, offset);
    },
    Iterable_iterableToShortString(iterable, leftDelimiter, rightDelimiter) {
      var parts, t1;
      if (A.isToStringVisiting(iterable)) {
        if (leftDelimiter === "(" && rightDelimiter === ")")
          return "(...)";
        return leftDelimiter + "..." + rightDelimiter;
      }
      parts = A._setArrayType([], type$.JSArray_String);
      B.JSArray_methods.add$1($.toStringVisiting, iterable);
      try {
        A._iterablePartsToStrings(iterable, parts);
      } finally {
        if (0 >= $.toStringVisiting.length)
          return A.ioore($.toStringVisiting, -1);
        $.toStringVisiting.pop();
      }
      t1 = A.StringBuffer__writeAll(leftDelimiter, type$.Iterable_dynamic._as(parts), ", ") + rightDelimiter;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    },
    Iterable_iterableToFullString(iterable, leftDelimiter, rightDelimiter) {
      var buffer, t1;
      if (A.isToStringVisiting(iterable))
        return leftDelimiter + "..." + rightDelimiter;
      buffer = new A.StringBuffer(leftDelimiter);
      B.JSArray_methods.add$1($.toStringVisiting, iterable);
      try {
        t1 = buffer;
        t1._contents = A.StringBuffer__writeAll(t1._contents, iterable, ", ");
      } finally {
        if (0 >= $.toStringVisiting.length)
          return A.ioore($.toStringVisiting, -1);
        $.toStringVisiting.pop();
      }
      buffer._contents += rightDelimiter;
      t1 = buffer._contents;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    },
    _iterablePartsToStrings(iterable, parts) {
      var next, ultimateString, penultimateString, penultimate, ultimate, ultimate0, elision,
        it = iterable.get$iterator(iterable),
        $length = 0, count = 0;
      while (true) {
        if (!($length < 80 || count < 3))
          break;
        if (!it.moveNext$0())
          return;
        next = A.S(it.get$current());
        B.JSArray_methods.add$1(parts, next);
        $length += next.length + 2;
        ++count;
      }
      if (!it.moveNext$0()) {
        if (count <= 5)
          return;
        if (0 >= parts.length)
          return A.ioore(parts, -1);
        ultimateString = parts.pop();
        if (0 >= parts.length)
          return A.ioore(parts, -1);
        penultimateString = parts.pop();
      } else {
        penultimate = it.get$current();
        ++count;
        if (!it.moveNext$0()) {
          if (count <= 4) {
            B.JSArray_methods.add$1(parts, A.S(penultimate));
            return;
          }
          ultimateString = A.S(penultimate);
          if (0 >= parts.length)
            return A.ioore(parts, -1);
          penultimateString = parts.pop();
          $length += ultimateString.length + 2;
        } else {
          ultimate = it.get$current();
          ++count;
          for (; it.moveNext$0(); penultimate = ultimate, ultimate = ultimate0) {
            ultimate0 = it.get$current();
            ++count;
            if (count > 100) {
              while (true) {
                if (!($length > 75 && count > 3))
                  break;
                if (0 >= parts.length)
                  return A.ioore(parts, -1);
                $length -= parts.pop().length + 2;
                --count;
              }
              B.JSArray_methods.add$1(parts, "...");
              return;
            }
          }
          penultimateString = A.S(penultimate);
          ultimateString = A.S(ultimate);
          $length += ultimateString.length + penultimateString.length + 4;
        }
      }
      if (count > parts.length + 2) {
        $length += 5;
        elision = "...";
      } else
        elision = null;
      while (true) {
        if (!($length > 80 && parts.length > 3))
          break;
        if (0 >= parts.length)
          return A.ioore(parts, -1);
        $length -= parts.pop().length + 2;
        if (elision == null) {
          $length += 5;
          elision = "...";
        }
      }
      if (elision != null)
        B.JSArray_methods.add$1(parts, elision);
      B.JSArray_methods.add$1(parts, penultimateString);
      B.JSArray_methods.add$1(parts, ultimateString);
    },
    Object_hash(object1, object2) {
      var t1 = B.JSInt_methods.get$hashCode(object1);
      object2 = B.JSInt_methods.get$hashCode(object2);
      object2 = A.SystemHash_finish(A.SystemHash_combine(A.SystemHash_combine($.$get$_hashSeed(), t1), object2));
      return object2;
    },
    NoSuchMethodError_toString_closure: function NoSuchMethodError_toString_closure(t0, t1) {
      this._box_0 = t0;
      this.sb = t1;
    },
    DateTime: function DateTime(t0, t1, t2) {
      this._value = t0;
      this._microsecond = t1;
      this.isUtc = t2;
    },
    _Enum: function _Enum() {
    },
    Error: function Error() {
    },
    AssertionError: function AssertionError(t0) {
      this.message = t0;
    },
    TypeError: function TypeError() {
    },
    ArgumentError: function ArgumentError(t0, t1, t2, t3) {
      var _ = this;
      _._hasValue = t0;
      _.invalidValue = t1;
      _.name = t2;
      _.message = t3;
    },
    RangeError: function RangeError(t0, t1, t2, t3, t4, t5) {
      var _ = this;
      _.start = t0;
      _.end = t1;
      _._hasValue = t2;
      _.invalidValue = t3;
      _.name = t4;
      _.message = t5;
    },
    IndexError: function IndexError(t0, t1, t2, t3, t4) {
      var _ = this;
      _.length = t0;
      _._hasValue = t1;
      _.invalidValue = t2;
      _.name = t3;
      _.message = t4;
    },
    NoSuchMethodError: function NoSuchMethodError(t0, t1, t2, t3) {
      var _ = this;
      _._core$_receiver = t0;
      _._core$_memberName = t1;
      _._core$_arguments = t2;
      _._namedArguments = t3;
    },
    UnsupportedError: function UnsupportedError(t0) {
      this.message = t0;
    },
    UnimplementedError: function UnimplementedError(t0) {
      this.message = t0;
    },
    StateError: function StateError(t0) {
      this.message = t0;
    },
    ConcurrentModificationError: function ConcurrentModificationError(t0) {
      this.modifiedObject = t0;
    },
    OutOfMemoryError: function OutOfMemoryError() {
    },
    StackOverflowError: function StackOverflowError() {
    },
    _Exception: function _Exception(t0) {
      this.message = t0;
    },
    FormatException: function FormatException(t0, t1, t2) {
      this.message = t0;
      this.source = t1;
      this.offset = t2;
    },
    Iterable: function Iterable() {
    },
    Null: function Null() {
    },
    Object: function Object() {
    },
    _StringStackTrace: function _StringStackTrace() {
    },
    StringBuffer: function StringBuffer(t0) {
      this._contents = t0;
    },
    _convertDartFunctionFast(f) {
      var ret,
        existing = f.$dart_jsFunction;
      if (existing != null)
        return existing;
      ret = function(_call, f) {
        return function() {
          return _call(f, Array.prototype.slice.apply(arguments));
        };
      }(A._callDartFunctionFast, f);
      ret[$.$get$DART_CLOSURE_PROPERTY_NAME()] = f;
      f.$dart_jsFunction = ret;
      return ret;
    },
    _callDartFunctionFast(callback, $arguments) {
      type$.List_dynamic._as($arguments);
      type$.Function._as(callback);
      return A.Primitives_applyFunction(callback, $arguments, null);
    },
    allowInterop(f, $F) {
      if (typeof f == "function")
        return f;
      else
        return $F._as(A._convertDartFunctionFast(f));
    },
    _functionToJS1(f) {
      var result;
      if (typeof f == "function")
        throw A.wrapException(A.ArgumentError$("Attempting to rewrap a JS function.", null));
      result = function(_call, f) {
        return function(arg1) {
          return _call(f, arg1, arguments.length);
        };
      }(A._callDartFunctionFast1, f);
      result[$.$get$DART_CLOSURE_PROPERTY_NAME()] = f;
      return result;
    },
    _callDartFunctionFast1(callback, arg1, $length) {
      type$.Function._as(callback);
      if (A._asInt($length) >= 1)
        return callback.call$1(arg1);
      return callback.call$0();
    },
    _noJsifyRequired(o) {
      return o == null || A._isBool(o) || typeof o == "number" || typeof o == "string" || type$.Int8List._is(o) || type$.Uint8List._is(o) || type$.Uint8ClampedList._is(o) || type$.Int16List._is(o) || type$.Uint16List._is(o) || type$.Int32List._is(o) || type$.Uint32List._is(o) || type$.Float32List._is(o) || type$.Float64List._is(o) || type$.ByteBuffer._is(o) || type$.ByteData._is(o);
    },
    jsify(object) {
      if (A._noJsifyRequired(object))
        return object;
      return new A.jsify__convert(new A._IdentityHashMap(type$._IdentityHashMap_of_nullable_Object_and_nullable_Object)).call$1(object);
    },
    callMethod(o, method, args, $T) {
      return $T._as(o[method].apply(o, args));
    },
    promiseToFuture(jsPromise, $T) {
      var t1 = new A._Future($.Zone__current, $T._eval$1("_Future<0>")),
        completer = new A._AsyncCompleter(t1, $T._eval$1("_AsyncCompleter<0>"));
      jsPromise.then(A.convertDartClosureToJS(new A.promiseToFuture_closure(completer, $T), 1), A.convertDartClosureToJS(new A.promiseToFuture_closure0(completer), 1));
      return t1;
    },
    _noDartifyRequired(o) {
      return o == null || typeof o === "boolean" || typeof o === "number" || typeof o === "string" || o instanceof Int8Array || o instanceof Uint8Array || o instanceof Uint8ClampedArray || o instanceof Int16Array || o instanceof Uint16Array || o instanceof Int32Array || o instanceof Uint32Array || o instanceof Float32Array || o instanceof Float64Array || o instanceof ArrayBuffer || o instanceof DataView;
    },
    dartify(o) {
      if (A._noDartifyRequired(o))
        return o;
      return new A.dartify_convert(new A._IdentityHashMap(type$._IdentityHashMap_of_nullable_Object_and_nullable_Object)).call$1(o);
    },
    jsify__convert: function jsify__convert(t0) {
      this._convertedObjects = t0;
    },
    promiseToFuture_closure: function promiseToFuture_closure(t0, t1) {
      this.completer = t0;
      this.T = t1;
    },
    promiseToFuture_closure0: function promiseToFuture_closure0(t0) {
      this.completer = t0;
    },
    dartify_convert: function dartify_convert(t0) {
      this._convertedObjects = t0;
    },
    NullRejectionException: function NullRejectionException(t0) {
      this.isUndefined = t0;
    },
    _JSSecureRandom: function _JSSecureRandom(t0) {
      this._math$_buffer = t0;
    },
    Level: function Level(t0, t1) {
      this.name = t0;
      this.value = t1;
    },
    LogRecord: function LogRecord(t0, t1, t2) {
      this.level = t0;
      this.message = t1;
      this.loggerName = t2;
    },
    Logger_Logger($name) {
      return $.Logger__loggers.putIfAbsent$2($name, new A.Logger_Logger_closure($name));
    },
    Logger: function Logger(t0, t1, t2) {
      var _ = this;
      _.name = t0;
      _.parent = t1;
      _._level = null;
      _._children = t2;
      _._controller = null;
    },
    Logger_Logger_closure: function Logger_Logger_closure(t0) {
      this.name = t0;
    },
    findNALUIndices(stream) {
      var start, pos0, t1, end,
        result = A._setArrayType([], type$.JSArray_int),
        pos = stream.length,
        searchLength = pos - 2;
      for (start = 0, pos0 = 0; pos0 < searchLength; start = pos0) {
        while (true) {
          if (pos0 < searchLength) {
            if (!(pos0 >= 0))
              return A.ioore(stream, pos0);
            t1 = !(stream[pos0] === 0 && stream[pos0 + 1] === 0 && stream[pos0 + 2] === 1);
          } else
            t1 = false;
          if (!t1)
            break;
          ++pos0;
        }
        if (pos0 >= searchLength)
          pos0 = pos;
        end = pos0;
        while (true) {
          if (end > start) {
            t1 = end - 1;
            if (!(t1 >= 0))
              return A.ioore(stream, t1);
            t1 = stream[t1] === 0;
          } else
            t1 = false;
          if (!t1)
            break;
          --end;
        }
        if (start === 0) {
          if (end !== start)
            throw A.wrapException(A.Exception_Exception("byte stream contains leading data"));
        } else
          B.JSArray_methods.add$1(result, start);
        pos0 += 3;
      }
      return result;
    },
    CryptorError: function CryptorError(t0) {
      this._name = t0;
    },
    FrameInfo: function FrameInfo(t0, t1, t2, t3) {
      var _ = this;
      _.frameType = t0;
      _.ssrc = t1;
      _.timestamp = t2;
      _.buffer = t3;
    },
    FrameCryptor: function FrameCryptor(t0, t1, t2, t3, t4, t5, t6) {
      var _ = this;
      _.sendCounts = t0;
      _.participantIdentity = t1;
      _.trackId = t2;
      _.codec = null;
      _.keyHandler = t3;
      _.__FrameCryptor_kind_A = $;
      _._enabled = false;
      _.lastError = t4;
      _.currentKeyIndex = 0;
      _.worker = t5;
      _.sifGuard = t6;
    },
    FrameCryptor_decodeFunction_decryptFrameInternal: function FrameCryptor_decodeFunction_decryptFrameInternal(t0, t1, t2, t3, t4, t5, t6) {
      var _ = this;
      _._box_1 = t0;
      _._box_0 = t1;
      _.$this = t2;
      _.iv = t3;
      _.srcFrame = t4;
      _.headerLength = t5;
      _.ivLength = t6;
    },
    FrameCryptor_decodeFunction_ratchedKeyInternal: function FrameCryptor_decodeFunction_ratchedKeyInternal(t0, t1, t2, t3) {
      var _ = this;
      _._box_1 = t0;
      _._box_0 = t1;
      _.$this = t2;
      _.decryptFrameInternal = t3;
    },
    ParticipantKeyHandler$(keyOptions, participantIdentity, worker) {
      var t1 = new A.ParticipantKeyHandler(keyOptions, worker, participantIdentity),
        t2 = keyOptions.keyRingSze;
      if (t2 <= 0 || t2 > 255)
        A.throwExpression(A.Exception_Exception("Invalid key ring size"));
      t1.set$__ParticipantKeyHandler_cryptoKeyRing_A(type$.List_nullable_KeySet._as(A.List_List$filled(t2, null, false, type$.nullable_KeySet)));
      return t1;
    },
    KeyOptions: function KeyOptions(t0, t1, t2, t3, t4, t5, t6) {
      var _ = this;
      _.sharedKey = t0;
      _.ratchetSalt = t1;
      _.ratchetWindowSize = t2;
      _.failureTolerance = t3;
      _.uncryptedMagicBytes = t4;
      _.keyRingSze = t5;
      _.discardFrameWhenCryptorNotReady = t6;
    },
    KeyProvider: function KeyProvider(t0, t1, t2, t3) {
      var _ = this;
      _.worker = t0;
      _.keyProviderOptions = t1;
      _.participantKeys = t2;
      _.sharedKeyHandler = null;
      _.sharedKey = t3;
    },
    KeySet: function KeySet(t0, t1) {
      this.material = t0;
      this.encryptionKey = t1;
    },
    ParticipantKeyHandler: function ParticipantKeyHandler(t0, t1, t2) {
      var _ = this;
      _.currentKeyIndex = 0;
      _.__ParticipantKeyHandler_cryptoKeyRing_A = $;
      _._hasValidKey = false;
      _.keyOptions = t0;
      _.worker = t1;
      _.participantIdentity = t2;
      _._decryptionFailureCount = 0;
    },
    SifGuard: function SifGuard() {
      var _ = this;
      _.consecutiveSifCount = 0;
      _.sifSequenceStartedAt = null;
      _.userFramesSinceSif = _.lastSifReceivedAt = 0;
    },
    getTrackCryptor(participantIdentity, trackId, keyProvider) {
      var t1, t2, _null = null,
        cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.getTrackCryptor_closure(trackId), type$.FrameCryptor);
      if (cryptor == null) {
        $.$get$logger().log$4(B.Level_INFO_800, "creating new cryptor for " + participantIdentity + ", trackId " + trackId, _null, _null);
        t1 = type$.JSObject._as(self.self);
        t2 = type$.int;
        cryptor = new A.FrameCryptor(A.LinkedHashMap_LinkedHashMap$_empty(t2, t2), participantIdentity, trackId, keyProvider.getParticipantKeyHandler$1(participantIdentity), B.CryptorError_0, t1, new A.SifGuard());
        B.JSArray_methods.add$1($.participantCryptors, cryptor);
      } else if (participantIdentity !== cryptor.participantIdentity) {
        t1 = keyProvider.getParticipantKeyHandler$1(participantIdentity);
        if (cryptor.lastError !== B.CryptorError_1) {
          $.$get$logger().log$4(B.Level_INFO_800, "setParticipantId: lastError != CryptorError.kOk, reset state to kNew", _null, _null);
          cryptor.lastError = B.CryptorError_0;
        }
        cryptor.participantIdentity = participantIdentity;
        cryptor.keyHandler = t1;
        cryptor.sifGuard.reset$0();
      }
      return cryptor;
    },
    unsetCryptorParticipant(trackId) {
      var t1 = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.unsetCryptorParticipant_closure(trackId), type$.FrameCryptor);
      if (t1 != null)
        t1.participantIdentity = null;
    },
    main() {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        t2, t3, t1;
      var $async$main = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $.$get$Logger_root();
              if (t1.parent != null)
                A.throwExpression(A.UnsupportedError$('Please set "hierarchicalLoggingEnabled" to true if you want to change the level on a non-root logger.'));
              J.$eq$(t1._level, B.Level_WARNING_900);
              t1._level = B.Level_WARNING_900;
              t1._getStream$0().listen$1(new A.main_closure());
              t1 = $.$get$logger();
              t1.log$4(B.Level_INFO_800, "Worker created", null, null);
              t2 = self;
              t3 = type$.JSObject;
              if ("RTCTransformEvent" in t3._as(t2.self)) {
                t1.log$4(B.Level_INFO_800, "setup RTCTransformEvent event handler", null, null);
                t3._as(t2.self).onrtctransform = A._functionToJS1(new A.main_closure0());
              }
              t3._as(t2.self).onmessage = A._functionToJS1(new A.main_closure1(new A.main_closure2()));
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$main, $async$completer);
    },
    getTrackCryptor_closure: function getTrackCryptor_closure(t0) {
      this.trackId = t0;
    },
    unsetCryptorParticipant_closure: function unsetCryptorParticipant_closure(t0) {
      this.trackId = t0;
    },
    main_closure: function main_closure() {
    },
    main_closure0: function main_closure0() {
    },
    main_closure2: function main_closure2() {
    },
    main__closure: function main__closure(t0) {
      this.trackId = t0;
    },
    main__closure0: function main__closure0(t0) {
      this.trackId = t0;
    },
    main__closure1: function main__closure1(t0) {
      this.trackId = t0;
    },
    main__closure2: function main__closure2(t0) {
      this.trackId = t0;
    },
    main_closure1: function main_closure1(t0) {
      this.handleMessage = t0;
    },
    printString(string) {
      if (typeof dartPrint == "function") {
        dartPrint(string);
        return;
      }
      if (typeof console == "object" && typeof console.log != "undefined") {
        console.log(string);
        return;
      }
      if (typeof print == "function") {
        print(string);
        return;
      }
      throw "Unable to print message: " + String(string);
    },
    throwLateFieldNI(fieldName) {
      A.throwExpressionWithWrapper(new A.LateError("Field '" + fieldName + "' has not been initialized."), new Error());
    },
    throwLateFieldADI(fieldName) {
      A.throwExpressionWithWrapper(new A.LateError("Field '" + fieldName + "' has been assigned during initialization."), new Error());
    },
    IterableExtension_firstWhereOrNull(_this, test, $T) {
      var t1, _i, element;
      for (t1 = _this.length, _i = 0; _i < _this.length; _this.length === t1 || (0, A.throwConcurrentModificationError)(_this), ++_i) {
        element = _this[_i];
        if (A.boolConversionCheck(test.call$1(element)))
          return element;
      }
      return null;
    },
    getAlgoOptions(algorithmName, salt) {
      switch (algorithmName) {
        case "HKDF":
          return A.LinkedHashMap_LinkedHashMap$_literal(["name", "HKDF", "salt", salt, "hash", "SHA-256", "info", new Uint8Array(128)], type$.String, type$.dynamic);
        case "PBKDF2":
          return A.LinkedHashMap_LinkedHashMap$_literal(["name", "PBKDF2", "salt", salt, "hash", "SHA-256", "iterations", 100000], type$.String, type$.dynamic);
        default:
          throw A.wrapException(A.Exception_Exception("algorithm " + algorithmName + " is currently unsupported"));
      }
    }
  },
  B = {};
  var holders = [A, J, B];
  var $ = {};
  A.JS_CONST.prototype = {};
  J.Interceptor.prototype = {
    $eq(receiver, other) {
      return receiver === other;
    },
    get$hashCode(receiver) {
      return A.Primitives_objectHashCode(receiver);
    },
    toString$0(receiver) {
      return "Instance of '" + A.Primitives_objectTypeName(receiver) + "'";
    },
    noSuchMethod$1(receiver, invocation) {
      throw A.wrapException(A.NoSuchMethodError_NoSuchMethodError$withInvocation(receiver, type$.Invocation._as(invocation)));
    },
    get$runtimeType(receiver) {
      return A.createRuntimeType(A._instanceTypeFromConstructor(this));
    }
  };
  J.JSBool.prototype = {
    toString$0(receiver) {
      return String(receiver);
    },
    get$hashCode(receiver) {
      return receiver ? 519018 : 218159;
    },
    get$runtimeType(receiver) {
      return A.createRuntimeType(type$.bool);
    },
    $isTrustedGetRuntimeType: 1,
    $isbool: 1
  };
  J.JSNull.prototype = {
    $eq(receiver, other) {
      return null == other;
    },
    toString$0(receiver) {
      return "null";
    },
    get$hashCode(receiver) {
      return 0;
    },
    $isTrustedGetRuntimeType: 1,
    $isNull: 1
  };
  J.JavaScriptObject.prototype = {$isJSObject: 1};
  J.LegacyJavaScriptObject.prototype = {
    get$hashCode(receiver) {
      return 0;
    },
    get$runtimeType(receiver) {
      return B.Type_JSObject_ttY;
    },
    toString$0(receiver) {
      return String(receiver);
    }
  };
  J.PlainJavaScriptObject.prototype = {};
  J.UnknownJavaScriptObject.prototype = {};
  J.JavaScriptFunction.prototype = {
    toString$0(receiver) {
      var dartClosure = receiver[$.$get$DART_CLOSURE_PROPERTY_NAME()];
      if (dartClosure == null)
        return this.super$LegacyJavaScriptObject$toString(receiver);
      return "JavaScript function for " + J.toString$0$(dartClosure);
    },
    $isFunction: 1
  };
  J.JavaScriptBigInt.prototype = {
    get$hashCode(receiver) {
      return 0;
    },
    toString$0(receiver) {
      return String(receiver);
    }
  };
  J.JavaScriptSymbol.prototype = {
    get$hashCode(receiver) {
      return 0;
    },
    toString$0(receiver) {
      return String(receiver);
    }
  };
  J.JSArray.prototype = {
    add$1(receiver, value) {
      A._arrayInstanceType(receiver)._precomputed1._as(value);
      receiver.$flags & 1 && A.throwUnsupportedOperation(receiver, 29);
      receiver.push(value);
    },
    addAll$1(receiver, collection) {
      var t1;
      A._arrayInstanceType(receiver)._eval$1("Iterable<1>")._as(collection);
      receiver.$flags & 1 && A.throwUnsupportedOperation(receiver, "addAll", 2);
      if (Array.isArray(collection)) {
        this._addAllFromArray$1(receiver, collection);
        return;
      }
      for (t1 = J.get$iterator$ax(collection); t1.moveNext$0();)
        receiver.push(t1.get$current());
    },
    _addAllFromArray$1(receiver, array) {
      var len, i;
      type$.JSArray_dynamic._as(array);
      len = array.length;
      if (len === 0)
        return;
      if (receiver === array)
        throw A.wrapException(A.ConcurrentModificationError$(receiver));
      for (i = 0; i < len; ++i)
        receiver.push(array[i]);
    },
    map$1$1(receiver, f, $T) {
      var t1 = A._arrayInstanceType(receiver);
      return new A.MappedListIterable(receiver, t1._bind$1($T)._eval$1("1(2)")._as(f), t1._eval$1("@<1>")._bind$1($T)._eval$1("MappedListIterable<1,2>"));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    toString$0(receiver) {
      return A.Iterable_iterableToFullString(receiver, "[", "]");
    },
    get$iterator(receiver) {
      return new J.ArrayIterator(receiver, receiver.length, A._arrayInstanceType(receiver)._eval$1("ArrayIterator<1>"));
    },
    get$hashCode(receiver) {
      return A.Primitives_objectHashCode(receiver);
    },
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (!(index >= 0 && index < receiver.length))
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      A._arrayInstanceType(receiver)._precomputed1._as(value);
      receiver.$flags & 2 && A.throwUnsupportedOperation(receiver);
      if (!(index >= 0 && index < receiver.length))
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      receiver[index] = value;
    },
    get$runtimeType(receiver) {
      return A.createRuntimeType(A._arrayInstanceType(receiver));
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  J.JSUnmodifiableArray.prototype = {};
  J.ArrayIterator.prototype = {
    get$current() {
      var t1 = this._current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var t2, _this = this,
        t1 = _this._iterable,
        $length = t1.length;
      if (_this._length !== $length) {
        t1 = A.throwConcurrentModificationError(t1);
        throw A.wrapException(t1);
      }
      t2 = _this._index;
      if (t2 >= $length) {
        _this.set$_current(null);
        return false;
      }
      _this.set$_current(t1[t2]);
      ++_this._index;
      return true;
    },
    set$_current(_current) {
      this._current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  J.JSNumber.prototype = {
    toInt$0(receiver) {
      var t1;
      if (receiver >= -2147483648 && receiver <= 2147483647)
        return receiver | 0;
      if (isFinite(receiver)) {
        t1 = receiver < 0 ? Math.ceil(receiver) : Math.floor(receiver);
        return t1 + 0;
      }
      throw A.wrapException(A.UnsupportedError$("" + receiver + ".toInt()"));
    },
    toRadixString$1(receiver, radix) {
      var result, t1, t2, match, exponent;
      if (radix < 2 || radix > 36)
        throw A.wrapException(A.RangeError$range(radix, 2, 36, "radix", null));
      result = receiver.toString(radix);
      t1 = result.length;
      t2 = t1 - 1;
      if (!(t2 >= 0))
        return A.ioore(result, t2);
      if (result.charCodeAt(t2) !== 41)
        return result;
      match = /^([\da-z]+)(?:\.([\da-z]+))?\(e\+(\d+)\)$/.exec(result);
      if (match == null)
        A.throwExpression(A.UnsupportedError$("Unexpected toString result: " + result));
      t1 = match.length;
      if (1 >= t1)
        return A.ioore(match, 1);
      result = match[1];
      if (3 >= t1)
        return A.ioore(match, 3);
      exponent = +match[3];
      t1 = match[2];
      if (t1 != null) {
        result += t1;
        exponent -= t1.length;
      }
      return result + B.JSString_methods.$mul("0", exponent);
    },
    toString$0(receiver) {
      if (receiver === 0 && 1 / receiver < 0)
        return "-0.0";
      else
        return "" + receiver;
    },
    get$hashCode(receiver) {
      var absolute, floorLog2, factor, scaled,
        intValue = receiver | 0;
      if (receiver === intValue)
        return intValue & 536870911;
      absolute = Math.abs(receiver);
      floorLog2 = Math.log(absolute) / 0.6931471805599453 | 0;
      factor = Math.pow(2, floorLog2);
      scaled = absolute < 1 ? absolute / factor : factor / absolute;
      return ((scaled * 9007199254740992 | 0) + (scaled * 3542243181176521 | 0)) * 599197 + floorLog2 * 1259 & 536870911;
    },
    $mod(receiver, other) {
      var result = receiver % other;
      if (result === 0)
        return 0;
      if (result > 0)
        return result;
      return result + other;
    },
    _tdivFast$1(receiver, other) {
      return (receiver | 0) === receiver ? receiver / other | 0 : this._tdivSlow$1(receiver, other);
    },
    _tdivSlow$1(receiver, other) {
      var quotient = receiver / other;
      if (quotient >= -2147483648 && quotient <= 2147483647)
        return quotient | 0;
      if (quotient > 0) {
        if (quotient !== 1 / 0)
          return Math.floor(quotient);
      } else if (quotient > -1 / 0)
        return Math.ceil(quotient);
      throw A.wrapException(A.UnsupportedError$("Result of truncating division is " + A.S(quotient) + ": " + A.S(receiver) + " ~/ " + other));
    },
    _shrOtherPositive$1(receiver, other) {
      var t1;
      if (receiver > 0)
        t1 = this._shrBothPositive$1(receiver, other);
      else {
        t1 = other > 31 ? 31 : other;
        t1 = receiver >> t1 >>> 0;
      }
      return t1;
    },
    _shrBothPositive$1(receiver, other) {
      return other > 31 ? 0 : receiver >>> other;
    },
    get$runtimeType(receiver) {
      return A.createRuntimeType(type$.num);
    },
    $isdouble: 1,
    $isnum: 1
  };
  J.JSInt.prototype = {
    get$runtimeType(receiver) {
      return A.createRuntimeType(type$.int);
    },
    $isTrustedGetRuntimeType: 1,
    $isint: 1
  };
  J.JSNumNotInt.prototype = {
    get$runtimeType(receiver) {
      return A.createRuntimeType(type$.double);
    },
    $isTrustedGetRuntimeType: 1
  };
  J.JSString.prototype = {
    endsWith$1(receiver, other) {
      var otherLength = other.length,
        t1 = receiver.length;
      if (otherLength > t1)
        return false;
      return other === this.substring$1(receiver, t1 - otherLength);
    },
    startsWith$1(receiver, pattern) {
      var otherLength = pattern.length;
      if (otherLength > receiver.length)
        return false;
      return pattern === receiver.substring(0, otherLength);
    },
    substring$2(receiver, start, end) {
      return receiver.substring(start, A.RangeError_checkValidRange(start, end, receiver.length));
    },
    substring$1(receiver, start) {
      return this.substring$2(receiver, start, null);
    },
    $mul(receiver, times) {
      var s, result;
      if (0 >= times)
        return "";
      if (times === 1 || receiver.length === 0)
        return receiver;
      if (times !== times >>> 0)
        throw A.wrapException(B.C_OutOfMemoryError);
      for (s = receiver, result = ""; true;) {
        if ((times & 1) === 1)
          result = s + result;
        times = times >>> 1;
        if (times === 0)
          break;
        s += s;
      }
      return result;
    },
    lastIndexOf$1(receiver, pattern) {
      var start = receiver.length,
        t1 = pattern.length;
      if (start + t1 > start)
        start -= t1;
      return receiver.lastIndexOf(pattern, start);
    },
    toString$0(receiver) {
      return receiver;
    },
    get$hashCode(receiver) {
      var t1, hash, i;
      for (t1 = receiver.length, hash = 0, i = 0; i < t1; ++i) {
        hash = hash + receiver.charCodeAt(i) & 536870911;
        hash = hash + ((hash & 524287) << 10) & 536870911;
        hash ^= hash >> 6;
      }
      hash = hash + ((hash & 67108863) << 3) & 536870911;
      hash ^= hash >> 11;
      return hash + ((hash & 16383) << 15) & 536870911;
    },
    get$runtimeType(receiver) {
      return A.createRuntimeType(type$.String);
    },
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (!(index.$ge(0, 0) && index.$lt(0, receiver.length)))
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isPattern: 1,
    $isString: 1
  };
  A._CopyingBytesBuilder.prototype = {
    add$1(_, bytes) {
      var byteCount, required, t1, t2, newSize, x, newBuffer, _this = this;
      type$.List_int._as(bytes);
      byteCount = bytes.length;
      if (byteCount === 0)
        return;
      required = _this.__internal$_length + byteCount;
      t1 = _this._buffer;
      t2 = t1.length;
      if (t2 < required) {
        newSize = required * 2;
        if (newSize < 1024)
          newSize = 1024;
        else {
          x = newSize - 1;
          x |= B.JSInt_methods._shrOtherPositive$1(x, 1);
          x |= x >>> 2;
          x |= x >>> 4;
          x |= x >>> 8;
          newSize = ((x | x >>> 16) >>> 0) + 1;
        }
        newBuffer = new Uint8Array(newSize);
        B.NativeUint8List_methods.setRange$3(newBuffer, 0, t2, t1);
        _this._buffer = newBuffer;
        t1 = newBuffer;
      }
      B.NativeUint8List_methods.setRange$3(t1, _this.__internal$_length, required, bytes);
      _this.__internal$_length = required;
    },
    toBytes$0() {
      var _this = this;
      if (_this.__internal$_length === 0)
        return $.$get$_CopyingBytesBuilder__emptyList();
      return new Uint8Array(A._ensureNativeList(J.asUint8List$2$x(B.NativeUint8List_methods.get$buffer(_this._buffer), _this._buffer.byteOffset, _this.__internal$_length)));
    },
    get$length(_) {
      return this.__internal$_length;
    },
    $isBytesBuilder: 1
  };
  A.LateError.prototype = {
    toString$0(_) {
      return "LateInitializationError: " + this._message;
    }
  };
  A.SentinelValue.prototype = {};
  A.EfficientLengthIterable.prototype = {};
  A.ListIterable.prototype = {
    get$iterator(_) {
      var _this = this;
      return new A.ListIterator(_this, _this.get$length(_this), A._instanceType(_this)._eval$1("ListIterator<ListIterable.E>"));
    },
    map$1$1(_, toElement, $T) {
      var t1 = A._instanceType(this);
      return new A.MappedListIterable(this, t1._bind$1($T)._eval$1("1(ListIterable.E)")._as(toElement), t1._eval$1("@<ListIterable.E>")._bind$1($T)._eval$1("MappedListIterable<1,2>"));
    }
  };
  A.ListIterator.prototype = {
    get$current() {
      var t1 = this.__internal$_current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var t3, _this = this,
        t1 = _this.__internal$_iterable,
        t2 = J.getInterceptor$asx(t1),
        $length = t2.get$length(t1);
      if (_this.__internal$_length !== $length)
        throw A.wrapException(A.ConcurrentModificationError$(t1));
      t3 = _this.__internal$_index;
      if (t3 >= $length) {
        _this.set$__internal$_current(null);
        return false;
      }
      _this.set$__internal$_current(t2.elementAt$1(t1, t3));
      ++_this.__internal$_index;
      return true;
    },
    set$__internal$_current(_current) {
      this.__internal$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.MappedIterable.prototype = {
    get$iterator(_) {
      var t1 = this.__internal$_iterable;
      return new A.MappedIterator(t1.get$iterator(t1), this._f, A._instanceType(this)._eval$1("MappedIterator<1,2>"));
    },
    get$length(_) {
      var t1 = this.__internal$_iterable;
      return t1.get$length(t1);
    }
  };
  A.EfficientLengthMappedIterable.prototype = {$isEfficientLengthIterable: 1};
  A.MappedIterator.prototype = {
    moveNext$0() {
      var _this = this,
        t1 = _this._iterator;
      if (t1.moveNext$0()) {
        _this.set$__internal$_current(_this._f.call$1(t1.get$current()));
        return true;
      }
      _this.set$__internal$_current(null);
      return false;
    },
    get$current() {
      var t1 = this.__internal$_current;
      return t1 == null ? this.$ti._rest[1]._as(t1) : t1;
    },
    set$__internal$_current(_current) {
      this.__internal$_current = this.$ti._eval$1("2?")._as(_current);
    },
    $isIterator: 1
  };
  A.MappedListIterable.prototype = {
    get$length(_) {
      return J.get$length$asx(this._source);
    },
    elementAt$1(_, index) {
      return this._f.call$1(J.elementAt$1$ax(this._source, index));
    }
  };
  A.WhereIterable.prototype = {
    get$iterator(_) {
      return new A.WhereIterator(J.get$iterator$ax(this.__internal$_iterable), this._f, this.$ti._eval$1("WhereIterator<1>"));
    },
    map$1$1(_, toElement, $T) {
      var t1 = this.$ti;
      return new A.MappedIterable(this, t1._bind$1($T)._eval$1("1(2)")._as(toElement), t1._eval$1("@<1>")._bind$1($T)._eval$1("MappedIterable<1,2>"));
    }
  };
  A.WhereIterator.prototype = {
    moveNext$0() {
      var t1, t2;
      for (t1 = this._iterator, t2 = this._f; t1.moveNext$0();)
        if (A.boolConversionCheck(t2.call$1(t1.get$current())))
          return true;
      return false;
    },
    get$current() {
      return this._iterator.get$current();
    },
    $isIterator: 1
  };
  A.FixedLengthListMixin.prototype = {};
  A.Symbol.prototype = {
    get$hashCode(_) {
      var hash = this._hashCode;
      if (hash != null)
        return hash;
      hash = 664597 * B.JSString_methods.get$hashCode(this.__internal$_name) & 536870911;
      this._hashCode = hash;
      return hash;
    },
    toString$0(_) {
      return 'Symbol("' + this.__internal$_name + '")';
    },
    $eq(_, other) {
      if (other == null)
        return false;
      return other instanceof A.Symbol && this.__internal$_name === other.__internal$_name;
    },
    $isSymbol0: 1
  };
  A.ConstantMapView.prototype = {};
  A.ConstantMap.prototype = {
    toString$0(_) {
      return A.MapBase_mapToString(this);
    },
    $isMap: 1
  };
  A.ConstantStringMap.prototype = {
    get$length(_) {
      return this._values.length;
    },
    get$_keys() {
      var keys = this.$keys;
      if (keys == null) {
        keys = Object.keys(this._jsIndex);
        this.$keys = keys;
      }
      return keys;
    },
    containsKey$1(key) {
      if (typeof key != "string")
        return false;
      if ("__proto__" === key)
        return false;
      return this._jsIndex.hasOwnProperty(key);
    },
    $index(_, key) {
      if (!this.containsKey$1(key))
        return null;
      return this._values[this._jsIndex[key]];
    },
    forEach$1(_, f) {
      var keys, values, t1, i;
      this.$ti._eval$1("~(1,2)")._as(f);
      keys = this.get$_keys();
      values = this._values;
      for (t1 = keys.length, i = 0; i < t1; ++i)
        f.call$2(keys[i], values[i]);
    },
    get$keys() {
      return new A._KeysOrValues(this.get$_keys(), this.$ti._eval$1("_KeysOrValues<1>"));
    }
  };
  A._KeysOrValues.prototype = {
    get$length(_) {
      return this._elements.length;
    },
    get$iterator(_) {
      var t1 = this._elements;
      return new A._KeysOrValuesOrElementsIterator(t1, t1.length, this.$ti._eval$1("_KeysOrValuesOrElementsIterator<1>"));
    }
  };
  A._KeysOrValuesOrElementsIterator.prototype = {
    get$current() {
      var t1 = this.__js_helper$_current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var _this = this,
        t1 = _this.__js_helper$_index;
      if (t1 >= _this.__js_helper$_length) {
        _this.set$__js_helper$_current(null);
        return false;
      }
      _this.set$__js_helper$_current(_this._elements[t1]);
      ++_this.__js_helper$_index;
      return true;
    },
    set$__js_helper$_current(_current) {
      this.__js_helper$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.JSInvocationMirror.prototype = {
    get$memberName() {
      var t1 = this._memberName;
      if (t1 instanceof A.Symbol)
        return t1;
      return this._memberName = new A.Symbol(A._asString(t1));
    },
    get$positionalArguments() {
      var t1, t2, argumentCount, list, index, _this = this;
      if (_this.__js_helper$_kind === 1)
        return B.List_empty;
      t1 = _this._arguments;
      t2 = J.getInterceptor$asx(t1);
      argumentCount = t2.get$length(t1) - J.get$length$asx(_this._namedArgumentNames) - _this._typeArgumentCount;
      if (argumentCount === 0)
        return B.List_empty;
      list = [];
      for (index = 0; index < argumentCount; ++index)
        list.push(t2.$index(t1, index));
      list.$flags = 3;
      return list;
    },
    get$namedArguments() {
      var t1, t2, namedArgumentCount, t3, t4, namedArgumentsStartIndex, map, i, _this = this;
      if (_this.__js_helper$_kind !== 0)
        return B.Map_empty;
      t1 = _this._namedArgumentNames;
      t2 = J.getInterceptor$asx(t1);
      namedArgumentCount = t2.get$length(t1);
      t3 = _this._arguments;
      t4 = J.getInterceptor$asx(t3);
      namedArgumentsStartIndex = t4.get$length(t3) - namedArgumentCount - _this._typeArgumentCount;
      if (namedArgumentCount === 0)
        return B.Map_empty;
      map = new A.JsLinkedHashMap(type$.JsLinkedHashMap_Symbol_dynamic);
      for (i = 0; i < namedArgumentCount; ++i)
        map.$indexSet(0, new A.Symbol(A._asString(t2.$index(t1, i))), t4.$index(t3, namedArgumentsStartIndex + i));
      return new A.ConstantMapView(map, type$.ConstantMapView_Symbol_dynamic);
    },
    $isInvocation: 1
  };
  A.Primitives_functionNoSuchMethod_closure.prototype = {
    call$2($name, argument) {
      var t1;
      A._asString($name);
      t1 = this._box_0;
      t1.names = t1.names + "$" + $name;
      B.JSArray_methods.add$1(this.namedArgumentList, $name);
      B.JSArray_methods.add$1(this.$arguments, argument);
      ++t1.argumentCount;
    },
    $signature: 12
  };
  A.TypeErrorDecoder.prototype = {
    matchTypeError$1(message) {
      var result, t1, _this = this,
        match = new RegExp(_this._pattern).exec(message);
      if (match == null)
        return null;
      result = Object.create(null);
      t1 = _this._arguments;
      if (t1 !== -1)
        result.arguments = match[t1 + 1];
      t1 = _this._argumentsExpr;
      if (t1 !== -1)
        result.argumentsExpr = match[t1 + 1];
      t1 = _this._expr;
      if (t1 !== -1)
        result.expr = match[t1 + 1];
      t1 = _this._method;
      if (t1 !== -1)
        result.method = match[t1 + 1];
      t1 = _this._receiver;
      if (t1 !== -1)
        result.receiver = match[t1 + 1];
      return result;
    }
  };
  A.NullError.prototype = {
    toString$0(_) {
      return "Null check operator used on a null value";
    }
  };
  A.JsNoSuchMethodError.prototype = {
    toString$0(_) {
      var t2, _this = this,
        _s38_ = "NoSuchMethodError: method not found: '",
        t1 = _this._method;
      if (t1 == null)
        return "NoSuchMethodError: " + _this.__js_helper$_message;
      t2 = _this._receiver;
      if (t2 == null)
        return _s38_ + t1 + "' (" + _this.__js_helper$_message + ")";
      return _s38_ + t1 + "' on '" + t2 + "' (" + _this.__js_helper$_message + ")";
    }
  };
  A.UnknownJsTypeError.prototype = {
    toString$0(_) {
      var t1 = this.__js_helper$_message;
      return t1.length === 0 ? "Error" : "Error: " + t1;
    }
  };
  A.NullThrownFromJavaScriptException.prototype = {
    toString$0(_) {
      return "Throw of null ('" + (this._irritant === null ? "null" : "undefined") + "' from JavaScript)";
    }
  };
  A.ExceptionAndStackTrace.prototype = {};
  A._StackTrace.prototype = {
    toString$0(_) {
      var trace,
        t1 = this._trace;
      if (t1 != null)
        return t1;
      t1 = this._exception;
      trace = t1 !== null && typeof t1 === "object" ? t1.stack : null;
      return this._trace = trace == null ? "" : trace;
    },
    $isStackTrace: 1
  };
  A.Closure.prototype = {
    toString$0(_) {
      var $constructor = this.constructor,
        $name = $constructor == null ? null : $constructor.name;
      return "Closure '" + A.unminifyOrTag($name == null ? "unknown" : $name) + "'";
    },
    $isFunction: 1,
    get$$call() {
      return this;
    },
    "call*": "call$1",
    $requiredArgCount: 1,
    $defaultValues: null
  };
  A.Closure0Args.prototype = {"call*": "call$0", $requiredArgCount: 0};
  A.Closure2Args.prototype = {"call*": "call$2", $requiredArgCount: 2};
  A.TearOffClosure.prototype = {};
  A.StaticClosure.prototype = {
    toString$0(_) {
      var $name = this.$static_name;
      if ($name == null)
        return "Closure of unknown static method";
      return "Closure '" + A.unminifyOrTag($name) + "'";
    }
  };
  A.BoundClosure.prototype = {
    $eq(_, other) {
      if (other == null)
        return false;
      if (this === other)
        return true;
      if (!(other instanceof A.BoundClosure))
        return false;
      return this.$_target === other.$_target && this._receiver === other._receiver;
    },
    get$hashCode(_) {
      return (A.objectHashCode(this._receiver) ^ A.Primitives_objectHashCode(this.$_target)) >>> 0;
    },
    toString$0(_) {
      return "Closure '" + this.$_name + "' of " + ("Instance of '" + A.Primitives_objectTypeName(this._receiver) + "'");
    }
  };
  A._CyclicInitializationError.prototype = {
    toString$0(_) {
      return "Reading static variable '" + this.variableName + "' during its initialization";
    }
  };
  A.RuntimeError.prototype = {
    toString$0(_) {
      return "RuntimeError: " + this.message;
    }
  };
  A._AssertionError.prototype = {
    toString$0(_) {
      return "Assertion failed: " + A.Error_safeToString(this.message);
    }
  };
  A._Required.prototype = {};
  A.JsLinkedHashMap.prototype = {
    get$length(_) {
      return this.__js_helper$_length;
    },
    get$keys() {
      return new A.LinkedHashMapKeysIterable(this, A._instanceType(this)._eval$1("LinkedHashMapKeysIterable<1>"));
    },
    containsKey$1(key) {
      var strings = this._strings;
      if (strings == null)
        return false;
      return strings[key] != null;
    },
    $index(_, key) {
      var strings, cell, t1, nums, _null = null;
      if (typeof key == "string") {
        strings = this._strings;
        if (strings == null)
          return _null;
        cell = strings[key];
        t1 = cell == null ? _null : cell.hashMapCellValue;
        return t1;
      } else if (typeof key == "number" && (key & 0x3fffffff) === key) {
        nums = this._nums;
        if (nums == null)
          return _null;
        cell = nums[key];
        t1 = cell == null ? _null : cell.hashMapCellValue;
        return t1;
      } else
        return this.internalGet$1(key);
    },
    internalGet$1(key) {
      var bucket, index,
        rest = this.__js_helper$_rest;
      if (rest == null)
        return null;
      bucket = rest[this.internalComputeHashCode$1(key)];
      index = this.internalFindBucketIndex$2(bucket, key);
      if (index < 0)
        return null;
      return bucket[index].hashMapCellValue;
    },
    $indexSet(_, key, value) {
      var strings, nums, rest, hash, bucket, index, _this = this,
        t1 = A._instanceType(_this);
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      if (typeof key == "string") {
        strings = _this._strings;
        _this.__js_helper$_addHashTableEntry$3(strings == null ? _this._strings = _this._newHashTable$0() : strings, key, value);
      } else if (typeof key == "number" && (key & 0x3fffffff) === key) {
        nums = _this._nums;
        _this.__js_helper$_addHashTableEntry$3(nums == null ? _this._nums = _this._newHashTable$0() : nums, key, value);
      } else {
        rest = _this.__js_helper$_rest;
        if (rest == null)
          rest = _this.__js_helper$_rest = _this._newHashTable$0();
        hash = _this.internalComputeHashCode$1(key);
        bucket = rest[hash];
        if (bucket == null)
          rest[hash] = [_this._newLinkedCell$2(key, value)];
        else {
          index = _this.internalFindBucketIndex$2(bucket, key);
          if (index >= 0)
            bucket[index].hashMapCellValue = value;
          else
            bucket.push(_this._newLinkedCell$2(key, value));
        }
      }
    },
    putIfAbsent$2(key, ifAbsent) {
      var t2, value, _this = this,
        t1 = A._instanceType(_this);
      t1._precomputed1._as(key);
      t1._eval$1("2()")._as(ifAbsent);
      if (_this.containsKey$1(key)) {
        t2 = _this.$index(0, key);
        return t2 == null ? t1._rest[1]._as(t2) : t2;
      }
      value = ifAbsent.call$0();
      _this.$indexSet(0, key, value);
      return value;
    },
    remove$1(_, key) {
      var t1 = this._removeHashTableEntry$2(this._strings, key);
      return t1;
    },
    forEach$1(_, action) {
      var cell, modifications, _this = this;
      A._instanceType(_this)._eval$1("~(1,2)")._as(action);
      cell = _this._first;
      modifications = _this._modifications;
      for (; cell != null;) {
        action.call$2(cell.hashMapCellKey, cell.hashMapCellValue);
        if (modifications !== _this._modifications)
          throw A.wrapException(A.ConcurrentModificationError$(_this));
        cell = cell._next;
      }
    },
    __js_helper$_addHashTableEntry$3(table, key, value) {
      var cell,
        t1 = A._instanceType(this);
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      cell = table[key];
      if (cell == null)
        table[key] = this._newLinkedCell$2(key, value);
      else
        cell.hashMapCellValue = value;
    },
    _removeHashTableEntry$2(table, key) {
      var cell;
      if (table == null)
        return null;
      cell = table[key];
      if (cell == null)
        return null;
      this._unlinkCell$1(cell);
      delete table[key];
      return cell.hashMapCellValue;
    },
    _modified$0() {
      this._modifications = this._modifications + 1 & 1073741823;
    },
    _newLinkedCell$2(key, value) {
      var _this = this,
        t1 = A._instanceType(_this),
        cell = new A.LinkedHashMapCell(t1._precomputed1._as(key), t1._rest[1]._as(value));
      if (_this._first == null)
        _this._first = _this._last = cell;
      else {
        t1 = _this._last;
        t1.toString;
        cell._previous = t1;
        _this._last = t1._next = cell;
      }
      ++_this.__js_helper$_length;
      _this._modified$0();
      return cell;
    },
    _unlinkCell$1(cell) {
      var _this = this,
        previous = cell._previous,
        next = cell._next;
      if (previous == null)
        _this._first = next;
      else
        previous._next = next;
      if (next == null)
        _this._last = previous;
      else
        next._previous = previous;
      --_this.__js_helper$_length;
      _this._modified$0();
    },
    internalComputeHashCode$1(key) {
      return J.get$hashCode$(key) & 1073741823;
    },
    internalFindBucketIndex$2(bucket, key) {
      var $length, i;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; ++i)
        if (J.$eq$(bucket[i].hashMapCellKey, key))
          return i;
      return -1;
    },
    toString$0(_) {
      return A.MapBase_mapToString(this);
    },
    _newHashTable$0() {
      var table = Object.create(null);
      table["<non-identifier-key>"] = table;
      delete table["<non-identifier-key>"];
      return table;
    },
    $isLinkedHashMap: 1
  };
  A.LinkedHashMapCell.prototype = {};
  A.LinkedHashMapKeysIterable.prototype = {
    get$length(_) {
      return this._map.__js_helper$_length;
    },
    get$iterator(_) {
      var t1 = this._map;
      return new A.LinkedHashMapKeyIterator(t1, t1._modifications, t1._first, this.$ti._eval$1("LinkedHashMapKeyIterator<1>"));
    }
  };
  A.LinkedHashMapKeyIterator.prototype = {
    get$current() {
      return this.__js_helper$_current;
    },
    moveNext$0() {
      var cell, _this = this,
        t1 = _this._map;
      if (_this._modifications !== t1._modifications)
        throw A.wrapException(A.ConcurrentModificationError$(t1));
      cell = _this._cell;
      if (cell == null) {
        _this.set$__js_helper$_current(null);
        return false;
      } else {
        _this.set$__js_helper$_current(cell.hashMapCellKey);
        _this._cell = cell._next;
        return true;
      }
    },
    set$__js_helper$_current(_current) {
      this.__js_helper$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.initHooks_closure.prototype = {
    call$1(o) {
      return this.getTag(o);
    },
    $signature: 13
  };
  A.initHooks_closure0.prototype = {
    call$2(o, tag) {
      return this.getUnknownTag(o, tag);
    },
    $signature: 14
  };
  A.initHooks_closure1.prototype = {
    call$1(tag) {
      return this.prototypeForTag(A._asString(tag));
    },
    $signature: 15
  };
  A.NativeByteBuffer.prototype = {
    get$runtimeType(receiver) {
      return B.Type_ByteBuffer_rqD;
    },
    asUint8List$2(receiver, offsetInBytes, $length) {
      return $length == null ? new Uint8Array(receiver, offsetInBytes) : new Uint8Array(receiver, offsetInBytes, $length);
    },
    asUint8List$0(receiver) {
      return this.asUint8List$2(receiver, 0, null);
    },
    $isTrustedGetRuntimeType: 1,
    $isNativeByteBuffer: 1,
    $isByteBuffer: 1
  };
  A.NativeTypedData.prototype = {
    get$buffer(receiver) {
      if (((receiver.$flags | 0) & 2) !== 0)
        return new A._UnmodifiableNativeByteBufferView(receiver.buffer);
      else
        return receiver.buffer;
    },
    _invalidPosition$3(receiver, position, $length, $name) {
      var t1 = A.RangeError$range(position, 0, $length, $name, null);
      throw A.wrapException(t1);
    },
    _checkPosition$3(receiver, position, $length, $name) {
      if (position >>> 0 !== position || position > $length)
        this._invalidPosition$3(receiver, position, $length, $name);
    }
  };
  A._UnmodifiableNativeByteBufferView.prototype = {
    asUint8List$2(_, offsetInBytes, $length) {
      var result = A.NativeUint8List_NativeUint8List$view(this._data, offsetInBytes, $length);
      result.$flags = 3;
      return result;
    },
    asUint8List$0(_) {
      return this.asUint8List$2(0, 0, null);
    },
    $isByteBuffer: 1
  };
  A.NativeByteData.prototype = {
    get$runtimeType(receiver) {
      return B.Type_ByteData_9dB;
    },
    _setInt8$2(receiver, byteOffset, value) {
      return receiver.setInt8(byteOffset, value);
    },
    $isTrustedGetRuntimeType: 1,
    $isByteData: 1
  };
  A.NativeTypedArray.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $isJavaScriptIndexingBehavior: 1
  };
  A.NativeTypedArrayOfDouble.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.NativeTypedArrayOfInt.prototype = {
    setRange$3(receiver, start, end, iterable) {
      var targetLength, count, sourceLength, source;
      type$.Iterable_int._as(iterable);
      receiver.$flags & 2 && A.throwUnsupportedOperation(receiver, 5);
      targetLength = receiver.length;
      this._checkPosition$3(receiver, start, targetLength, "start");
      this._checkPosition$3(receiver, end, targetLength, "end");
      if (start > end)
        A.throwExpression(A.RangeError$range(start, 0, end, null, null));
      count = end - start;
      sourceLength = iterable.length;
      if (sourceLength < count)
        A.throwExpression(A.StateError$("Not enough elements"));
      source = sourceLength !== count ? iterable.subarray(0, count) : iterable;
      receiver.set(source, start);
      return;
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.NativeFloat32List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Float32List_9Kz;
    },
    $isTrustedGetRuntimeType: 1,
    $isFloat32List: 1
  };
  A.NativeFloat64List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Float64List_9Kz;
    },
    $isTrustedGetRuntimeType: 1,
    $isFloat64List: 1
  };
  A.NativeInt16List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Int16List_s5h;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isInt16List: 1
  };
  A.NativeInt32List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Int32List_O8Z;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isInt32List: 1
  };
  A.NativeInt8List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Int8List_rFV;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isInt8List: 1
  };
  A.NativeUint16List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Uint16List_kmP;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isUint16List: 1
  };
  A.NativeUint32List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Uint32List_kmP;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isUint32List: 1
  };
  A.NativeUint8ClampedList.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Uint8ClampedList_04U;
    },
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $isTrustedGetRuntimeType: 1,
    $isUint8ClampedList: 1
  };
  A.NativeUint8List.prototype = {
    get$runtimeType(receiver) {
      return B.Type_Uint8List_8Eb;
    },
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    sublist$2(receiver, start, end) {
      return new Uint8Array(receiver.subarray(start, A._checkValidRange(start, end, receiver.length)));
    },
    sublist$1(receiver, start) {
      return this.sublist$2(receiver, start, null);
    },
    $isTrustedGetRuntimeType: 1,
    $isUint8List: 1
  };
  A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin.prototype = {};
  A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin.prototype = {};
  A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin.prototype = {};
  A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin.prototype = {};
  A.Rti.prototype = {
    _eval$1(recipe) {
      return A._Universe_evalInEnvironment(init.typeUniverse, this, recipe);
    },
    _bind$1(typeOrTuple) {
      return A._Universe_bind(init.typeUniverse, this, typeOrTuple);
    }
  };
  A._FunctionParameters.prototype = {};
  A._Type.prototype = {
    toString$0(_) {
      return A._rtiToString(this._rti, null);
    }
  };
  A._Error.prototype = {
    toString$0(_) {
      return this.__rti$_message;
    }
  };
  A._TypeError.prototype = {$isTypeError: 1};
  A._AsyncRun__initializeScheduleImmediate_internalCallback.prototype = {
    call$1(__wc0_formal) {
      var t1 = this._box_0,
        f = t1.storedCallback;
      t1.storedCallback = null;
      f.call$0();
    },
    $signature: 2
  };
  A._AsyncRun__initializeScheduleImmediate_closure.prototype = {
    call$1(callback) {
      var t1, t2;
      this._box_0.storedCallback = type$.void_Function._as(callback);
      t1 = this.div;
      t2 = this.span;
      t1.firstChild ? t1.removeChild(t2) : t1.appendChild(t2);
    },
    $signature: 16
  };
  A._AsyncRun__scheduleImmediateJsOverride_internalCallback.prototype = {
    call$0() {
      this.callback.call$0();
    },
    $signature: 5
  };
  A._AsyncRun__scheduleImmediateWithSetImmediate_internalCallback.prototype = {
    call$0() {
      this.callback.call$0();
    },
    $signature: 5
  };
  A._TimerImpl.prototype = {
    _TimerImpl$2(milliseconds, callback) {
      if (self.setTimeout != null)
        self.setTimeout(A.convertDartClosureToJS(new A._TimerImpl_internalCallback(this, callback), 0), milliseconds);
      else
        throw A.wrapException(A.UnsupportedError$("`setTimeout()` not found."));
    }
  };
  A._TimerImpl_internalCallback.prototype = {
    call$0() {
      this.callback.call$0();
    },
    $signature: 0
  };
  A._AsyncAwaitCompleter.prototype = {
    complete$1(value) {
      var t2, _this = this,
        t1 = _this.$ti;
      t1._eval$1("1/?")._as(value);
      if (value == null)
        value = t1._precomputed1._as(value);
      if (!_this.isSync)
        _this._future._asyncComplete$1(value);
      else {
        t2 = _this._future;
        if (t1._eval$1("Future<1>")._is(value))
          t2._chainFuture$1(value);
        else
          t2._completeWithValue$1(value);
      }
    },
    completeError$2(e, st) {
      var t1 = this._future;
      if (this.isSync)
        t1._completeError$2(e, st);
      else
        t1._asyncCompleteError$2(e, st);
    }
  };
  A._awaitOnObject_closure.prototype = {
    call$1(result) {
      return this.bodyFunction.call$2(0, result);
    },
    $signature: 3
  };
  A._awaitOnObject_closure0.prototype = {
    call$2(error, stackTrace) {
      this.bodyFunction.call$2(1, new A.ExceptionAndStackTrace(error, type$.StackTrace._as(stackTrace)));
    },
    $signature: 17
  };
  A._wrapJsFunctionForAsync_closure.prototype = {
    call$2(errorCode, result) {
      this.$protected(A._asInt(errorCode), result);
    },
    $signature: 18
  };
  A.AsyncError.prototype = {
    toString$0(_) {
      return A.S(this.error);
    },
    $isError: 1,
    get$stackTrace() {
      return this.stackTrace;
    }
  };
  A._BroadcastStream.prototype = {};
  A._BroadcastSubscription.prototype = {
    _onPause$0() {
    },
    _onResume$0() {
    },
    set$_async$_next(_next) {
      this._async$_next = this.$ti._eval$1("_BroadcastSubscription<1>?")._as(_next);
    },
    set$_async$_previous(_previous) {
      this._async$_previous = this.$ti._eval$1("_BroadcastSubscription<1>?")._as(_previous);
    }
  };
  A._BroadcastStreamController.prototype = {
    get$_mayAddEvent() {
      return this._state < 4;
    },
    _subscribe$4(onData, onError, onDone, cancelOnError) {
      var t2, t3, t4, t5, subscription, oldLast, _this = this,
        t1 = A._instanceType(_this);
      t1._eval$1("~(1)?")._as(onData);
      type$.nullable_void_Function._as(onDone);
      if ((_this._state & 4) !== 0) {
        t1 = new A._DoneStreamSubscription($.Zone__current, t1._eval$1("_DoneStreamSubscription<1>"));
        A.scheduleMicrotask(t1.get$_onMicrotask());
        if (onDone != null)
          t1.set$_onDone(type$.void_Function._as(onDone));
        return t1;
      }
      t2 = $.Zone__current;
      t3 = cancelOnError ? 1 : 0;
      t4 = onError != null ? 32 : 0;
      type$.$env_1_1_void._bind$1(t1._precomputed1)._eval$1("1(2)")._as(onData);
      A._BufferingStreamSubscription__registerErrorHandler(t2, onError);
      t5 = onDone == null ? A.async___nullDoneHandler$closure() : onDone;
      type$.void_Function._as(t5);
      t1 = t1._eval$1("_BroadcastSubscription<1>");
      subscription = new A._BroadcastSubscription(_this, onData, t2, t3 | t4, t1);
      subscription.set$_async$_previous(subscription);
      subscription.set$_async$_next(subscription);
      t1._as(subscription);
      subscription._eventState = _this._state & 1;
      oldLast = _this._lastSubscription;
      _this.set$_lastSubscription(subscription);
      subscription.set$_async$_next(null);
      subscription.set$_async$_previous(oldLast);
      if (oldLast == null)
        _this.set$_firstSubscription(subscription);
      else
        oldLast.set$_async$_next(subscription);
      if (_this._firstSubscription == _this._lastSubscription)
        A._runGuarded(_this.onListen);
      return subscription;
    },
    _addEventError$0() {
      if ((this._state & 4) !== 0)
        return new A.StateError("Cannot add new events after calling close");
      return new A.StateError("Cannot add new events while doing an addStream");
    },
    _forEachListener$1(action) {
      var t2, subscription, id, next, previous, _this = this,
        t1 = A._instanceType(_this);
      t1._eval$1("~(_BufferingStreamSubscription<1>)")._as(action);
      t2 = _this._state;
      if ((t2 & 2) !== 0)
        throw A.wrapException(A.StateError$(string$.Cannot));
      subscription = _this._firstSubscription;
      if (subscription == null)
        return;
      id = t2 & 1;
      _this._state = t2 ^ 3;
      for (t1 = t1._eval$1("_BroadcastSubscription<1>"); subscription != null;) {
        t2 = subscription._eventState;
        if ((t2 & 1) === id) {
          subscription._eventState = t2 | 2;
          action.call$1(subscription);
          t2 = subscription._eventState ^= 1;
          next = subscription._async$_next;
          if ((t2 & 4) !== 0) {
            t1._as(subscription);
            previous = subscription._async$_previous;
            if (previous == null)
              _this.set$_firstSubscription(next);
            else
              previous.set$_async$_next(next);
            if (next == null)
              _this.set$_lastSubscription(previous);
            else
              next.set$_async$_previous(previous);
            subscription.set$_async$_previous(subscription);
            subscription.set$_async$_next(subscription);
          }
          subscription._eventState &= 4294967293;
          subscription = next;
        } else
          subscription = subscription._async$_next;
      }
      _this._state &= 4294967293;
      if (_this._firstSubscription == null)
        _this._callOnCancel$0();
    },
    _callOnCancel$0() {
      if ((this._state & 4) !== 0)
        if (null.get$_mayComplete())
          null._asyncComplete$1(null);
      A._runGuarded(this.onCancel);
    },
    set$_firstSubscription(_firstSubscription) {
      this._firstSubscription = A._instanceType(this)._eval$1("_BroadcastSubscription<1>?")._as(_firstSubscription);
    },
    set$_lastSubscription(_lastSubscription) {
      this._lastSubscription = A._instanceType(this)._eval$1("_BroadcastSubscription<1>?")._as(_lastSubscription);
    },
    $isStreamController: 1,
    $is_StreamControllerLifecycle: 1,
    $is_EventDispatch: 1
  };
  A._SyncBroadcastStreamController.prototype = {
    get$_mayAddEvent() {
      return A._BroadcastStreamController.prototype.get$_mayAddEvent.call(this) && (this._state & 2) === 0;
    },
    _addEventError$0() {
      if ((this._state & 2) !== 0)
        return new A.StateError(string$.Cannot);
      return this.super$_BroadcastStreamController$_addEventError();
    },
    _sendData$1(data) {
      var t1, _this = this;
      _this.$ti._precomputed1._as(data);
      t1 = _this._firstSubscription;
      if (t1 == null)
        return;
      if (t1 === _this._lastSubscription) {
        _this._state |= 2;
        t1._add$1(data);
        _this._state &= 4294967293;
        if (_this._firstSubscription == null)
          _this._callOnCancel$0();
        return;
      }
      _this._forEachListener$1(new A._SyncBroadcastStreamController__sendData_closure(_this, data));
    }
  };
  A._SyncBroadcastStreamController__sendData_closure.prototype = {
    call$1(subscription) {
      this.$this.$ti._eval$1("_BufferingStreamSubscription<1>")._as(subscription)._add$1(this.data);
    },
    $signature() {
      return this.$this.$ti._eval$1("~(_BufferingStreamSubscription<1>)");
    }
  };
  A._Completer.prototype = {
    completeError$2(error, stackTrace) {
      var _0_0,
        t1 = this.future;
      if ((t1._state & 30) !== 0)
        throw A.wrapException(A.StateError$("Future already completed"));
      _0_0 = A._interceptUserError(error, stackTrace);
      t1._asyncCompleteError$2(_0_0.error, _0_0.stackTrace);
    },
    completeError$1(error) {
      return this.completeError$2(error, null);
    }
  };
  A._AsyncCompleter.prototype = {
    complete$1(value) {
      var t2,
        t1 = this.$ti;
      t1._eval$1("1/?")._as(value);
      t2 = this.future;
      if ((t2._state & 30) !== 0)
        throw A.wrapException(A.StateError$("Future already completed"));
      t2._asyncComplete$1(t1._eval$1("1/")._as(value));
    }
  };
  A._FutureListener.prototype = {
    matchesErrorTest$1(asyncError) {
      if ((this.state & 15) !== 6)
        return true;
      return this.result._zone.runUnary$2$2(type$.bool_Function_Object._as(this.callback), asyncError.error, type$.bool, type$.Object);
    },
    handleError$1(asyncError) {
      var exception, _this = this,
        errorCallback = _this.errorCallback,
        result = null,
        t1 = type$.dynamic,
        t2 = type$.Object,
        t3 = asyncError.error,
        t4 = _this.result._zone;
      if (type$.dynamic_Function_Object_StackTrace._is(errorCallback))
        result = t4.runBinary$3$3(errorCallback, t3, asyncError.stackTrace, t1, t2, type$.StackTrace);
      else
        result = t4.runUnary$2$2(type$.dynamic_Function_Object._as(errorCallback), t3, t1, t2);
      try {
        t1 = _this.$ti._eval$1("2/")._as(result);
        return t1;
      } catch (exception) {
        if (type$.TypeError._is(A.unwrapException(exception))) {
          if ((_this.state & 1) !== 0)
            throw A.wrapException(A.ArgumentError$("The error handler of Future.then must return a value of the returned future's type", "onError"));
          throw A.wrapException(A.ArgumentError$("The error handler of Future.catchError must return a value of the future's type", "onError"));
        } else
          throw exception;
      }
    }
  };
  A._Future.prototype = {
    then$1$2$onError(f, onError, $R) {
      var currentZone, result,
        t1 = this.$ti;
      t1._bind$1($R)._eval$1("1/(2)")._as(f);
      currentZone = $.Zone__current;
      if (currentZone === B.C__RootZone) {
        if (!type$.dynamic_Function_Object_StackTrace._is(onError) && !type$.dynamic_Function_Object._is(onError))
          throw A.wrapException(A.ArgumentError$value(onError, "onError", string$.Error_));
      } else {
        $R._eval$1("@<0/>")._bind$1(t1._precomputed1)._eval$1("1(2)")._as(f);
        onError = A._registerErrorHandler(onError, currentZone);
      }
      result = new A._Future(currentZone, $R._eval$1("_Future<0>"));
      this._addListener$1(new A._FutureListener(result, 3, f, onError, t1._eval$1("@<1>")._bind$1($R)._eval$1("_FutureListener<1,2>")));
      return result;
    },
    _thenAwait$1$2(f, onError, $E) {
      var result,
        t1 = this.$ti;
      t1._bind$1($E)._eval$1("1/(2)")._as(f);
      result = new A._Future($.Zone__current, $E._eval$1("_Future<0>"));
      this._addListener$1(new A._FutureListener(result, 19, f, onError, t1._eval$1("@<1>")._bind$1($E)._eval$1("_FutureListener<1,2>")));
      return result;
    },
    _setErrorObject$1(error) {
      this._state = this._state & 1 | 16;
      this._resultOrListeners = error;
    },
    _cloneResult$1(source) {
      this._state = source._state & 30 | this._state & 1;
      this._resultOrListeners = source._resultOrListeners;
    },
    _addListener$1(listener) {
      var source, _this = this,
        t1 = _this._state;
      if (t1 <= 3) {
        listener._nextListener = type$.nullable__FutureListener_dynamic_dynamic._as(_this._resultOrListeners);
        _this._resultOrListeners = listener;
      } else {
        if ((t1 & 4) !== 0) {
          source = type$._Future_dynamic._as(_this._resultOrListeners);
          if ((source._state & 24) === 0) {
            source._addListener$1(listener);
            return;
          }
          _this._cloneResult$1(source);
        }
        A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__addListener_closure(_this, listener)));
      }
    },
    _prependListeners$1(listeners) {
      var t1, existingListeners, next, cursor, next0, source, _this = this, _box_0 = {};
      _box_0.listeners = listeners;
      if (listeners == null)
        return;
      t1 = _this._state;
      if (t1 <= 3) {
        existingListeners = type$.nullable__FutureListener_dynamic_dynamic._as(_this._resultOrListeners);
        _this._resultOrListeners = listeners;
        if (existingListeners != null) {
          next = listeners._nextListener;
          for (cursor = listeners; next != null; cursor = next, next = next0)
            next0 = next._nextListener;
          cursor._nextListener = existingListeners;
        }
      } else {
        if ((t1 & 4) !== 0) {
          source = type$._Future_dynamic._as(_this._resultOrListeners);
          if ((source._state & 24) === 0) {
            source._prependListeners$1(listeners);
            return;
          }
          _this._cloneResult$1(source);
        }
        _box_0.listeners = _this._reverseListeners$1(listeners);
        A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__prependListeners_closure(_box_0, _this)));
      }
    },
    _removeListeners$0() {
      var current = type$.nullable__FutureListener_dynamic_dynamic._as(this._resultOrListeners);
      this._resultOrListeners = null;
      return this._reverseListeners$1(current);
    },
    _reverseListeners$1(listeners) {
      var current, prev, next;
      for (current = listeners, prev = null; current != null; prev = current, current = next) {
        next = current._nextListener;
        current._nextListener = prev;
      }
      return prev;
    },
    _chainForeignFuture$1(source) {
      var e, s, exception, _this = this;
      _this._state ^= 2;
      try {
        source.then$1$2$onError(new A._Future__chainForeignFuture_closure(_this), new A._Future__chainForeignFuture_closure0(_this), type$.Null);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        A.scheduleMicrotask(new A._Future__chainForeignFuture_closure1(_this, e, s));
      }
    },
    _completeWithValue$1(value) {
      var listeners, _this = this;
      _this.$ti._precomputed1._as(value);
      listeners = _this._removeListeners$0();
      _this._state = 8;
      _this._resultOrListeners = value;
      A._Future__propagateToListeners(_this, listeners);
    },
    _completeWithResultOf$1(source) {
      var t1, listeners, _this = this;
      if ((source._state & 16) !== 0) {
        t1 = _this._zone === source._zone;
        t1 = !(t1 || t1);
      } else
        t1 = false;
      if (t1)
        return;
      listeners = _this._removeListeners$0();
      _this._cloneResult$1(source);
      A._Future__propagateToListeners(_this, listeners);
    },
    _completeError$2(error, stackTrace) {
      var listeners;
      type$.Object._as(error);
      type$.StackTrace._as(stackTrace);
      listeners = this._removeListeners$0();
      this._setErrorObject$1(new A.AsyncError(error, stackTrace));
      A._Future__propagateToListeners(this, listeners);
    },
    _asyncComplete$1(value) {
      var t1 = this.$ti;
      t1._eval$1("1/")._as(value);
      if (t1._eval$1("Future<1>")._is(value)) {
        this._chainFuture$1(value);
        return;
      }
      this._asyncCompleteWithValue$1(value);
    },
    _asyncCompleteWithValue$1(value) {
      var _this = this;
      _this.$ti._precomputed1._as(value);
      _this._state ^= 2;
      A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__asyncCompleteWithValue_closure(_this, value)));
    },
    _chainFuture$1(value) {
      var t1 = this.$ti;
      t1._eval$1("Future<1>")._as(value);
      if (t1._is(value)) {
        A._Future__chainCoreFuture(value, this, false);
        return;
      }
      this._chainForeignFuture$1(value);
    },
    _asyncCompleteError$2(error, stackTrace) {
      this._state ^= 2;
      A._rootScheduleMicrotask(null, null, this._zone, type$.void_Function._as(new A._Future__asyncCompleteError_closure(this, error, stackTrace)));
    },
    $isFuture: 1
  };
  A._Future__addListener_closure.prototype = {
    call$0() {
      A._Future__propagateToListeners(this.$this, this.listener);
    },
    $signature: 0
  };
  A._Future__prependListeners_closure.prototype = {
    call$0() {
      A._Future__propagateToListeners(this.$this, this._box_0.listeners);
    },
    $signature: 0
  };
  A._Future__chainForeignFuture_closure.prototype = {
    call$1(value) {
      var error, stackTrace, exception,
        t1 = this.$this;
      t1._state ^= 2;
      try {
        t1._completeWithValue$1(t1.$ti._precomputed1._as(value));
      } catch (exception) {
        error = A.unwrapException(exception);
        stackTrace = A.getTraceFromException(exception);
        t1._completeError$2(error, stackTrace);
      }
    },
    $signature: 2
  };
  A._Future__chainForeignFuture_closure0.prototype = {
    call$2(error, stackTrace) {
      this.$this._completeError$2(type$.Object._as(error), type$.StackTrace._as(stackTrace));
    },
    $signature: 7
  };
  A._Future__chainForeignFuture_closure1.prototype = {
    call$0() {
      this.$this._completeError$2(this.e, this.s);
    },
    $signature: 0
  };
  A._Future__chainCoreFuture_closure.prototype = {
    call$0() {
      A._Future__chainCoreFuture(this._box_0.source, this.target, true);
    },
    $signature: 0
  };
  A._Future__asyncCompleteWithValue_closure.prototype = {
    call$0() {
      this.$this._completeWithValue$1(this.value);
    },
    $signature: 0
  };
  A._Future__asyncCompleteError_closure.prototype = {
    call$0() {
      this.$this._completeError$2(this.error, this.stackTrace);
    },
    $signature: 0
  };
  A._Future__propagateToListeners_handleWhenCompleteCallback.prototype = {
    call$0() {
      var e, s, t1, exception, t2, t3, originalSource, joinedResult, _this = this, completeResult = null;
      try {
        t1 = _this._box_0.listener;
        completeResult = t1.result._zone.run$1$1(type$.dynamic_Function._as(t1.callback), type$.dynamic);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        if (_this.hasError && type$.AsyncError._as(_this._box_1.source._resultOrListeners).error === e) {
          t1 = _this._box_0;
          t1.listenerValueOrError = type$.AsyncError._as(_this._box_1.source._resultOrListeners);
        } else {
          t1 = e;
          t2 = s;
          if (t2 == null)
            t2 = A.AsyncError_defaultStackTrace(t1);
          t3 = _this._box_0;
          t3.listenerValueOrError = new A.AsyncError(t1, t2);
          t1 = t3;
        }
        t1.listenerHasError = true;
        return;
      }
      if (completeResult instanceof A._Future && (completeResult._state & 24) !== 0) {
        if ((completeResult._state & 16) !== 0) {
          t1 = _this._box_0;
          t1.listenerValueOrError = type$.AsyncError._as(completeResult._resultOrListeners);
          t1.listenerHasError = true;
        }
        return;
      }
      if (completeResult instanceof A._Future) {
        originalSource = _this._box_1.source;
        joinedResult = new A._Future(originalSource._zone, originalSource.$ti);
        completeResult.then$1$2$onError(new A._Future__propagateToListeners_handleWhenCompleteCallback_closure(joinedResult, originalSource), new A._Future__propagateToListeners_handleWhenCompleteCallback_closure0(joinedResult), type$.void);
        t1 = _this._box_0;
        t1.listenerValueOrError = joinedResult;
        t1.listenerHasError = false;
      }
    },
    $signature: 0
  };
  A._Future__propagateToListeners_handleWhenCompleteCallback_closure.prototype = {
    call$1(__wc0_formal) {
      this.joinedResult._completeWithResultOf$1(this.originalSource);
    },
    $signature: 2
  };
  A._Future__propagateToListeners_handleWhenCompleteCallback_closure0.prototype = {
    call$2(e, s) {
      this.joinedResult._completeError$2(type$.Object._as(e), type$.StackTrace._as(s));
    },
    $signature: 7
  };
  A._Future__propagateToListeners_handleValueCallback.prototype = {
    call$0() {
      var e, s, t1, t2, t3, t4, t5, exception;
      try {
        t1 = this._box_0;
        t2 = t1.listener;
        t3 = t2.$ti;
        t4 = t3._precomputed1;
        t5 = t4._as(this.sourceResult);
        t1.listenerValueOrError = t2.result._zone.runUnary$2$2(t3._eval$1("2/(1)")._as(t2.callback), t5, t3._eval$1("2/"), t4);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        t1 = e;
        t2 = s;
        if (t2 == null)
          t2 = A.AsyncError_defaultStackTrace(t1);
        t3 = this._box_0;
        t3.listenerValueOrError = new A.AsyncError(t1, t2);
        t3.listenerHasError = true;
      }
    },
    $signature: 0
  };
  A._Future__propagateToListeners_handleError.prototype = {
    call$0() {
      var asyncError, e, s, t1, exception, t2, t3, _this = this;
      try {
        asyncError = type$.AsyncError._as(_this._box_1.source._resultOrListeners);
        t1 = _this._box_0;
        if (t1.listener.matchesErrorTest$1(asyncError) && t1.listener.errorCallback != null) {
          t1.listenerValueOrError = t1.listener.handleError$1(asyncError);
          t1.listenerHasError = false;
        }
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        t1 = type$.AsyncError._as(_this._box_1.source._resultOrListeners);
        if (t1.error === e) {
          t2 = _this._box_0;
          t2.listenerValueOrError = t1;
          t1 = t2;
        } else {
          t1 = e;
          t2 = s;
          if (t2 == null)
            t2 = A.AsyncError_defaultStackTrace(t1);
          t3 = _this._box_0;
          t3.listenerValueOrError = new A.AsyncError(t1, t2);
          t1 = t3;
        }
        t1.listenerHasError = true;
      }
    },
    $signature: 0
  };
  A._AsyncCallbackEntry.prototype = {};
  A.Stream.prototype = {
    get$length(_) {
      var t1 = {},
        future = new A._Future($.Zone__current, type$._Future_int);
      t1.count = 0;
      this.listen$4$cancelOnError$onDone$onError(new A.Stream_length_closure(t1, this), true, new A.Stream_length_closure0(t1, future), future.get$_completeError());
      return future;
    }
  };
  A.Stream_length_closure.prototype = {
    call$1(__wc0_formal) {
      this.$this.$ti._precomputed1._as(__wc0_formal);
      ++this._box_0.count;
    },
    $signature() {
      return this.$this.$ti._eval$1("~(1)");
    }
  };
  A.Stream_length_closure0.prototype = {
    call$0() {
      var t1 = this.future,
        t2 = t1.$ti,
        t3 = t2._eval$1("1/")._as(this._box_0.count),
        listeners = t1._removeListeners$0();
      t2._precomputed1._as(t3);
      t1._state = 8;
      t1._resultOrListeners = t3;
      A._Future__propagateToListeners(t1, listeners);
    },
    $signature: 0
  };
  A._ControllerStream.prototype = {
    get$hashCode(_) {
      return (A.Primitives_objectHashCode(this._async$_controller) ^ 892482866) >>> 0;
    },
    $eq(_, other) {
      if (other == null)
        return false;
      if (this === other)
        return true;
      return other instanceof A._BroadcastStream && other._async$_controller === this._async$_controller;
    }
  };
  A._ControllerSubscription.prototype = {
    _onPause$0() {
      A._instanceType(this._async$_controller)._eval$1("StreamSubscription<1>")._as(this);
    },
    _onResume$0() {
      A._instanceType(this._async$_controller)._eval$1("StreamSubscription<1>")._as(this);
    }
  };
  A._BufferingStreamSubscription.prototype = {
    _add$1(data) {
      var t2, _this = this,
        t1 = A._instanceType(_this);
      t1._precomputed1._as(data);
      t2 = _this._state;
      if ((t2 & 8) !== 0)
        return;
      if (t2 < 64)
        _this._sendData$1(data);
      else
        _this._addPending$1(new A._DelayedData(data, t1._eval$1("_DelayedData<1>")));
    },
    _onPause$0() {
    },
    _onResume$0() {
    },
    _addPending$1($event) {
      var lastEvent, t1, _this = this,
        pending = _this._pending;
      if (pending == null) {
        pending = new A._PendingEvents(A._instanceType(_this)._eval$1("_PendingEvents<1>"));
        _this.set$_pending(pending);
      }
      lastEvent = pending.lastPendingEvent;
      if (lastEvent == null)
        pending.firstPendingEvent = pending.lastPendingEvent = $event;
      else
        pending.lastPendingEvent = lastEvent.next = $event;
      t1 = _this._state;
      if ((t1 & 128) === 0) {
        t1 |= 128;
        _this._state = t1;
        if (t1 < 256)
          pending.schedule$1(_this);
      }
    },
    _sendData$1(data) {
      var t2, _this = this,
        t1 = A._instanceType(_this)._precomputed1;
      t1._as(data);
      t2 = _this._state;
      _this._state = t2 | 64;
      _this._zone.runUnaryGuarded$1$2(_this._onData, data, t1);
      _this._state &= 4294967231;
      _this._checkState$1((t2 & 4) !== 0);
    },
    _checkState$1(wasInputPaused) {
      var t2, isInputPaused, _this = this,
        t1 = _this._state;
      if ((t1 & 128) !== 0 && _this._pending.lastPendingEvent == null) {
        t1 = _this._state = t1 & 4294967167;
        t2 = false;
        if ((t1 & 4) !== 0)
          if (t1 < 256) {
            t2 = _this._pending;
            t2 = t2 == null ? null : t2.lastPendingEvent == null;
            t2 = t2 !== false;
          }
        if (t2) {
          t1 &= 4294967291;
          _this._state = t1;
        }
      }
      for (; true; wasInputPaused = isInputPaused) {
        if ((t1 & 8) !== 0) {
          _this.set$_pending(null);
          return;
        }
        isInputPaused = (t1 & 4) !== 0;
        if (wasInputPaused === isInputPaused)
          break;
        _this._state = t1 ^ 64;
        if (isInputPaused)
          _this._onPause$0();
        else
          _this._onResume$0();
        t1 = _this._state &= 4294967231;
      }
      if ((t1 & 128) !== 0 && t1 < 256)
        _this._pending.schedule$1(_this);
    },
    set$_pending(_pending) {
      this._pending = A._instanceType(this)._eval$1("_PendingEvents<1>?")._as(_pending);
    },
    $isStreamSubscription: 1,
    $is_EventDispatch: 1
  };
  A._StreamImpl.prototype = {
    listen$4$cancelOnError$onDone$onError(onData, cancelOnError, onDone, onError) {
      var t1 = this.$ti;
      t1._eval$1("~(1)?")._as(onData);
      type$.nullable_void_Function._as(onDone);
      return this._async$_controller._subscribe$4(t1._eval$1("~(1)?")._as(onData), onError, onDone, cancelOnError === true);
    },
    listen$1(onData) {
      return this.listen$4$cancelOnError$onDone$onError(onData, null, null, null);
    }
  };
  A._DelayedEvent.prototype = {};
  A._DelayedData.prototype = {};
  A._PendingEvents.prototype = {
    schedule$1(dispatch) {
      var t1, _this = this;
      _this.$ti._eval$1("_EventDispatch<1>")._as(dispatch);
      t1 = _this._state;
      if (t1 === 1)
        return;
      if (t1 >= 1) {
        _this._state = 1;
        return;
      }
      A.scheduleMicrotask(new A._PendingEvents_schedule_closure(_this, dispatch));
      _this._state = 1;
    }
  };
  A._PendingEvents_schedule_closure.prototype = {
    call$0() {
      var t2, $event, nextEvent,
        t1 = this.$this,
        oldState = t1._state;
      t1._state = 0;
      if (oldState === 3)
        return;
      t2 = t1.$ti._eval$1("_EventDispatch<1>")._as(this.dispatch);
      $event = t1.firstPendingEvent;
      nextEvent = $event.next;
      t1.firstPendingEvent = nextEvent;
      if (nextEvent == null)
        t1.lastPendingEvent = null;
      A._instanceType($event)._eval$1("_EventDispatch<1>")._as(t2)._sendData$1($event.value);
    },
    $signature: 0
  };
  A._DoneStreamSubscription.prototype = {
    _onMicrotask$0() {
      var _0_0, _this = this,
        unscheduledState = _this._state - 1;
      if (unscheduledState === 0) {
        _this._state = -1;
        _0_0 = _this._onDone;
        if (_0_0 != null) {
          _this.set$_onDone(null);
          _this._zone.runGuarded$1(_0_0);
        }
      } else
        _this._state = unscheduledState;
    },
    set$_onDone(_onDone) {
      this._onDone = type$.nullable_void_Function._as(_onDone);
    },
    $isStreamSubscription: 1
  };
  A._StreamIterator.prototype = {};
  A._Zone.prototype = {$isZone: 1};
  A._rootHandleError_closure.prototype = {
    call$0() {
      A.Error_throwWithStackTrace(this.error, this.stackTrace);
    },
    $signature: 0
  };
  A._RootZone.prototype = {
    runGuarded$1(f) {
      var e, s, exception;
      type$.void_Function._as(f);
      try {
        if (B.C__RootZone === $.Zone__current) {
          f.call$0();
          return;
        }
        A._rootRun(null, null, this, f, type$.void);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        A._rootHandleError(type$.Object._as(e), type$.StackTrace._as(s));
      }
    },
    runUnaryGuarded$1$2(f, arg, $T) {
      var e, s, exception;
      $T._eval$1("~(0)")._as(f);
      $T._as(arg);
      try {
        if (B.C__RootZone === $.Zone__current) {
          f.call$1(arg);
          return;
        }
        A._rootRunUnary(null, null, this, f, arg, type$.void, $T);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        A._rootHandleError(type$.Object._as(e), type$.StackTrace._as(s));
      }
    },
    bindCallbackGuarded$1(f) {
      return new A._RootZone_bindCallbackGuarded_closure(this, type$.void_Function._as(f));
    },
    $index(_, key) {
      return null;
    },
    run$1$1(f, $R) {
      $R._eval$1("0()")._as(f);
      if ($.Zone__current === B.C__RootZone)
        return f.call$0();
      return A._rootRun(null, null, this, f, $R);
    },
    runUnary$2$2(f, arg, $R, $T) {
      $R._eval$1("@<0>")._bind$1($T)._eval$1("1(2)")._as(f);
      $T._as(arg);
      if ($.Zone__current === B.C__RootZone)
        return f.call$1(arg);
      return A._rootRunUnary(null, null, this, f, arg, $R, $T);
    },
    runBinary$3$3(f, arg1, arg2, $R, T1, T2) {
      $R._eval$1("@<0>")._bind$1(T1)._bind$1(T2)._eval$1("1(2,3)")._as(f);
      T1._as(arg1);
      T2._as(arg2);
      if ($.Zone__current === B.C__RootZone)
        return f.call$2(arg1, arg2);
      return A._rootRunBinary(null, null, this, f, arg1, arg2, $R, T1, T2);
    },
    registerBinaryCallback$3$1(f, $R, T1, T2) {
      return $R._eval$1("@<0>")._bind$1(T1)._bind$1(T2)._eval$1("1(2,3)")._as(f);
    }
  };
  A._RootZone_bindCallbackGuarded_closure.prototype = {
    call$0() {
      return this.$this.runGuarded$1(this.f);
    },
    $signature: 0
  };
  A._HashMap.prototype = {
    get$length(_) {
      return this._collection$_length;
    },
    get$keys() {
      return new A._HashMapKeyIterable(this, this.$ti._eval$1("_HashMapKeyIterable<1>"));
    },
    containsKey$1(key) {
      var strings, nums;
      if (typeof key == "string" && key !== "__proto__") {
        strings = this._collection$_strings;
        return strings == null ? false : strings[key] != null;
      } else if (typeof key == "number" && (key & 1073741823) === key) {
        nums = this._collection$_nums;
        return nums == null ? false : nums[key] != null;
      } else
        return this._containsKey$1(key);
    },
    _containsKey$1(key) {
      var rest = this._collection$_rest;
      if (rest == null)
        return false;
      return this._findBucketIndex$2(this._getBucket$2(rest, key), key) >= 0;
    },
    $index(_, key) {
      var strings, t1, nums;
      if (typeof key == "string" && key !== "__proto__") {
        strings = this._collection$_strings;
        t1 = strings == null ? null : A._HashMap__getTableEntry(strings, key);
        return t1;
      } else if (typeof key == "number" && (key & 1073741823) === key) {
        nums = this._collection$_nums;
        t1 = nums == null ? null : A._HashMap__getTableEntry(nums, key);
        return t1;
      } else
        return this._get$1(key);
    },
    _get$1(key) {
      var bucket, index,
        rest = this._collection$_rest;
      if (rest == null)
        return null;
      bucket = this._getBucket$2(rest, key);
      index = this._findBucketIndex$2(bucket, key);
      return index < 0 ? null : bucket[index + 1];
    },
    $indexSet(_, key, value) {
      var strings, nums, rest, hash, bucket, index, _this = this,
        t1 = _this.$ti;
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      if (typeof key == "string" && key !== "__proto__") {
        strings = _this._collection$_strings;
        _this._addHashTableEntry$3(strings == null ? _this._collection$_strings = A._HashMap__newHashTable() : strings, key, value);
      } else if (typeof key == "number" && (key & 1073741823) === key) {
        nums = _this._collection$_nums;
        _this._addHashTableEntry$3(nums == null ? _this._collection$_nums = A._HashMap__newHashTable() : nums, key, value);
      } else {
        rest = _this._collection$_rest;
        if (rest == null)
          rest = _this._collection$_rest = A._HashMap__newHashTable();
        hash = A.objectHashCode(key) & 1073741823;
        bucket = rest[hash];
        if (bucket == null) {
          A._HashMap__setTableEntry(rest, hash, [key, value]);
          ++_this._collection$_length;
          _this._collection$_keys = null;
        } else {
          index = _this._findBucketIndex$2(bucket, key);
          if (index >= 0)
            bucket[index + 1] = value;
          else {
            bucket.push(key, value);
            ++_this._collection$_length;
            _this._collection$_keys = null;
          }
        }
      }
    },
    forEach$1(_, action) {
      var keys, $length, t2, i, key, t3, _this = this,
        t1 = _this.$ti;
      t1._eval$1("~(1,2)")._as(action);
      keys = _this._computeKeys$0();
      for ($length = keys.length, t2 = t1._precomputed1, t1 = t1._rest[1], i = 0; i < $length; ++i) {
        key = keys[i];
        t2._as(key);
        t3 = _this.$index(0, key);
        action.call$2(key, t3 == null ? t1._as(t3) : t3);
        if (keys !== _this._collection$_keys)
          throw A.wrapException(A.ConcurrentModificationError$(_this));
      }
    },
    _computeKeys$0() {
      var strings, index, names, entries, i, nums, rest, bucket, $length, i0, _this = this,
        result = _this._collection$_keys;
      if (result != null)
        return result;
      result = A.List_List$filled(_this._collection$_length, null, false, type$.dynamic);
      strings = _this._collection$_strings;
      index = 0;
      if (strings != null) {
        names = Object.getOwnPropertyNames(strings);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          result[index] = names[i];
          ++index;
        }
      }
      nums = _this._collection$_nums;
      if (nums != null) {
        names = Object.getOwnPropertyNames(nums);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          result[index] = +names[i];
          ++index;
        }
      }
      rest = _this._collection$_rest;
      if (rest != null) {
        names = Object.getOwnPropertyNames(rest);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          bucket = rest[names[i]];
          $length = bucket.length;
          for (i0 = 0; i0 < $length; i0 += 2) {
            result[index] = bucket[i0];
            ++index;
          }
        }
      }
      return _this._collection$_keys = result;
    },
    _addHashTableEntry$3(table, key, value) {
      var t1 = this.$ti;
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      if (table[key] == null) {
        ++this._collection$_length;
        this._collection$_keys = null;
      }
      A._HashMap__setTableEntry(table, key, value);
    },
    _getBucket$2(table, key) {
      return table[A.objectHashCode(key) & 1073741823];
    }
  };
  A._IdentityHashMap.prototype = {
    _findBucketIndex$2(bucket, key) {
      var $length, i, t1;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; i += 2) {
        t1 = bucket[i];
        if (t1 == null ? key == null : t1 === key)
          return i;
      }
      return -1;
    }
  };
  A._HashMapKeyIterable.prototype = {
    get$length(_) {
      return this._collection$_map._collection$_length;
    },
    get$iterator(_) {
      var t1 = this._collection$_map;
      return new A._HashMapKeyIterator(t1, t1._computeKeys$0(), this.$ti._eval$1("_HashMapKeyIterator<1>"));
    }
  };
  A._HashMapKeyIterator.prototype = {
    get$current() {
      var t1 = this._collection$_current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var _this = this,
        keys = _this._collection$_keys,
        offset = _this._offset,
        t1 = _this._collection$_map;
      if (keys !== t1._collection$_keys)
        throw A.wrapException(A.ConcurrentModificationError$(t1));
      else if (offset >= keys.length) {
        _this.set$_collection$_current(null);
        return false;
      } else {
        _this.set$_collection$_current(keys[offset]);
        _this._offset = offset + 1;
        return true;
      }
    },
    set$_collection$_current(_current) {
      this._collection$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.ListBase.prototype = {
    get$iterator(receiver) {
      return new A.ListIterator(receiver, this.get$length(receiver), A.instanceType(receiver)._eval$1("ListIterator<ListBase.E>"));
    },
    elementAt$1(receiver, index) {
      return this.$index(receiver, index);
    },
    map$1$1(receiver, f, $T) {
      var t1 = A.instanceType(receiver);
      return new A.MappedListIterable(receiver, t1._bind$1($T)._eval$1("1(ListBase.E)")._as(f), t1._eval$1("@<ListBase.E>")._bind$1($T)._eval$1("MappedListIterable<1,2>"));
    },
    toString$0(receiver) {
      return A.Iterable_iterableToFullString(receiver, "[", "]");
    }
  };
  A.MapBase.prototype = {
    forEach$1(_, action) {
      var t2, key, t3,
        t1 = A._instanceType(this);
      t1._eval$1("~(1,2)")._as(action);
      for (t2 = this.get$keys(), t2 = t2.get$iterator(t2), t1 = t1._rest[1]; t2.moveNext$0();) {
        key = t2.get$current();
        t3 = this.$index(0, key);
        action.call$2(key, t3 == null ? t1._as(t3) : t3);
      }
    },
    get$length(_) {
      var t1 = this.get$keys();
      return t1.get$length(t1);
    },
    toString$0(_) {
      return A.MapBase_mapToString(this);
    },
    $isMap: 1
  };
  A.MapBase_mapToString_closure.prototype = {
    call$2(k, v) {
      var t2,
        t1 = this._box_0;
      if (!t1.first)
        this.result._contents += ", ";
      t1.first = false;
      t1 = this.result;
      t2 = A.S(k);
      t2 = t1._contents += t2;
      t1._contents = t2 + ": ";
      t2 = A.S(v);
      t1._contents += t2;
    },
    $signature: 19
  };
  A._UnmodifiableMapMixin.prototype = {};
  A.MapView.prototype = {
    $index(_, key) {
      return this._collection$_map.$index(0, key);
    },
    forEach$1(_, action) {
      this._collection$_map.forEach$1(0, A._instanceType(this)._eval$1("~(1,2)")._as(action));
    },
    get$length(_) {
      return this._collection$_map.__js_helper$_length;
    },
    get$keys() {
      var t1 = this._collection$_map;
      return new A.LinkedHashMapKeysIterable(t1, A._instanceType(t1)._eval$1("LinkedHashMapKeysIterable<1>"));
    },
    toString$0(_) {
      return A.MapBase_mapToString(this._collection$_map);
    },
    $isMap: 1
  };
  A.UnmodifiableMapView.prototype = {};
  A._UnmodifiableMapView_MapView__UnmodifiableMapMixin.prototype = {};
  A.Base64Codec.prototype = {};
  A.Base64Encoder.prototype = {
    convert$1(input) {
      var t1;
      type$.List_int._as(input);
      t1 = input.length;
      if (t1 === 0)
        return "";
      t1 = new A._Base64Encoder("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/").encode$4(input, 0, t1, true);
      t1.toString;
      return A.String_String$fromCharCodes(t1);
    }
  };
  A._Base64Encoder.prototype = {
    encode$4(bytes, start, end, isLast) {
      var t1, byteCount, fullChunks, bufferLength, output;
      type$.List_int._as(bytes);
      t1 = this._convert$_state;
      byteCount = (t1 & 3) + (end - start);
      fullChunks = B.JSInt_methods._tdivFast$1(byteCount, 3);
      bufferLength = fullChunks * 4;
      if (byteCount - fullChunks * 3 > 0)
        bufferLength += 4;
      output = new Uint8Array(bufferLength);
      this._convert$_state = A._Base64Encoder_encodeChunk(this._alphabet, bytes, start, end, true, output, 0, t1);
      if (bufferLength > 0)
        return output;
      return null;
    }
  };
  A.Base64Decoder.prototype = {
    convert$1(input) {
      var decoder, t1, t2,
        end = A.RangeError_checkValidRange(0, null, input.length);
      if (0 === end)
        return new Uint8Array(0);
      decoder = new A._Base64Decoder();
      t1 = decoder.decode$3(input, 0, end);
      t1.toString;
      t2 = decoder._convert$_state;
      if (t2 < -1)
        A.throwExpression(A.FormatException$("Missing padding character", input, end));
      if (t2 > 0)
        A.throwExpression(A.FormatException$("Invalid length, must be multiple of four", input, end));
      decoder._convert$_state = -1;
      return t1;
    }
  };
  A._Base64Decoder.prototype = {
    decode$3(input, start, end) {
      var buffer, _this = this,
        t1 = _this._convert$_state;
      if (t1 < 0) {
        _this._convert$_state = A._Base64Decoder__checkPadding(input, start, end, t1);
        return null;
      }
      if (start === end)
        return new Uint8Array(0);
      buffer = A._Base64Decoder__allocateBuffer(input, start, end, t1);
      _this._convert$_state = A._Base64Decoder_decodeChunk(input, start, end, buffer, 0, _this._convert$_state);
      return buffer;
    }
  };
  A.Codec.prototype = {};
  A.Converter.prototype = {};
  A.NoSuchMethodError_toString_closure.prototype = {
    call$2(key, value) {
      var t1, t2, t3;
      type$.Symbol._as(key);
      t1 = this.sb;
      t2 = this._box_0;
      t3 = t1._contents += t2.comma;
      t3 += key.__internal$_name;
      t1._contents = t3;
      t1._contents = t3 + ": ";
      t3 = A.Error_safeToString(value);
      t1._contents += t3;
      t2.comma = ", ";
    },
    $signature: 20
  };
  A.DateTime.prototype = {
    $eq(_, other) {
      if (other == null)
        return false;
      return other instanceof A.DateTime && this._value === other._value && this._microsecond === other._microsecond && this.isUtc === other.isUtc;
    },
    get$hashCode(_) {
      return A.Object_hash(this._value, this._microsecond);
    },
    toString$0(_) {
      var _this = this,
        y = A.DateTime__fourDigits(A.Primitives_getYear(_this)),
        m = A.DateTime__twoDigits(A.Primitives_getMonth(_this)),
        d = A.DateTime__twoDigits(A.Primitives_getDay(_this)),
        h = A.DateTime__twoDigits(A.Primitives_getHours(_this)),
        min = A.DateTime__twoDigits(A.Primitives_getMinutes(_this)),
        sec = A.DateTime__twoDigits(A.Primitives_getSeconds(_this)),
        ms = A.DateTime__threeDigits(A.Primitives_getMilliseconds(_this)),
        t1 = _this._microsecond,
        us = t1 === 0 ? "" : A.DateTime__threeDigits(t1);
      t1 = y + "-" + m;
      if (_this.isUtc)
        return t1 + "-" + d + " " + h + ":" + min + ":" + sec + "." + ms + us + "Z";
      else
        return t1 + "-" + d + " " + h + ":" + min + ":" + sec + "." + ms + us;
    }
  };
  A._Enum.prototype = {
    toString$0(_) {
      return this._enumToString$0();
    }
  };
  A.Error.prototype = {
    get$stackTrace() {
      return A.Primitives_extractStackTrace(this);
    }
  };
  A.AssertionError.prototype = {
    toString$0(_) {
      var t1 = this.message;
      if (t1 != null)
        return "Assertion failed: " + A.Error_safeToString(t1);
      return "Assertion failed";
    }
  };
  A.TypeError.prototype = {};
  A.ArgumentError.prototype = {
    get$_errorName() {
      return "Invalid argument" + (!this._hasValue ? "(s)" : "");
    },
    get$_errorExplanation() {
      return "";
    },
    toString$0(_) {
      var _this = this,
        $name = _this.name,
        nameString = $name == null ? "" : " (" + $name + ")",
        message = _this.message,
        messageString = message == null ? "" : ": " + A.S(message),
        prefix = _this.get$_errorName() + nameString + messageString;
      if (!_this._hasValue)
        return prefix;
      return prefix + _this.get$_errorExplanation() + ": " + A.Error_safeToString(_this.get$invalidValue());
    },
    get$invalidValue() {
      return this.invalidValue;
    }
  };
  A.RangeError.prototype = {
    get$invalidValue() {
      return A._asNumQ(this.invalidValue);
    },
    get$_errorName() {
      return "RangeError";
    },
    get$_errorExplanation() {
      var explanation,
        start = this.start,
        end = this.end;
      if (start == null)
        explanation = end != null ? ": Not less than or equal to " + A.S(end) : "";
      else if (end == null)
        explanation = ": Not greater than or equal to " + A.S(start);
      else if (end > start)
        explanation = ": Not in inclusive range " + A.S(start) + ".." + A.S(end);
      else
        explanation = end < start ? ": Valid value range is empty" : ": Only valid value is " + A.S(start);
      return explanation;
    }
  };
  A.IndexError.prototype = {
    get$invalidValue() {
      return A._asInt(this.invalidValue);
    },
    get$_errorName() {
      return "RangeError";
    },
    get$_errorExplanation() {
      if (A._asInt(this.invalidValue) < 0)
        return ": index must not be negative";
      var t1 = this.length;
      if (t1 === 0)
        return ": no indices are valid";
      return ": index should be less than " + t1;
    },
    get$length(receiver) {
      return this.length;
    }
  };
  A.NoSuchMethodError.prototype = {
    toString$0(_) {
      var $arguments, t1, _i, t2, t3, argument, receiverText, actualParameters, _this = this, _box_0 = {},
        sb = new A.StringBuffer("");
      _box_0.comma = "";
      $arguments = _this._core$_arguments;
      for (t1 = $arguments.length, _i = 0, t2 = "", t3 = ""; _i < t1; ++_i, t3 = ", ") {
        argument = $arguments[_i];
        sb._contents = t2 + t3;
        t2 = A.Error_safeToString(argument);
        t2 = sb._contents += t2;
        _box_0.comma = ", ";
      }
      _this._namedArguments.forEach$1(0, new A.NoSuchMethodError_toString_closure(_box_0, sb));
      receiverText = A.Error_safeToString(_this._core$_receiver);
      actualParameters = sb.toString$0(0);
      return "NoSuchMethodError: method not found: '" + _this._core$_memberName.__internal$_name + "'\nReceiver: " + receiverText + "\nArguments: [" + actualParameters + "]";
    }
  };
  A.UnsupportedError.prototype = {
    toString$0(_) {
      return "Unsupported operation: " + this.message;
    }
  };
  A.UnimplementedError.prototype = {
    toString$0(_) {
      return "UnimplementedError: " + this.message;
    }
  };
  A.StateError.prototype = {
    toString$0(_) {
      return "Bad state: " + this.message;
    }
  };
  A.ConcurrentModificationError.prototype = {
    toString$0(_) {
      var t1 = this.modifiedObject;
      if (t1 == null)
        return "Concurrent modification during iteration.";
      return "Concurrent modification during iteration: " + A.Error_safeToString(t1) + ".";
    }
  };
  A.OutOfMemoryError.prototype = {
    toString$0(_) {
      return "Out of Memory";
    },
    get$stackTrace() {
      return null;
    },
    $isError: 1
  };
  A.StackOverflowError.prototype = {
    toString$0(_) {
      return "Stack Overflow";
    },
    get$stackTrace() {
      return null;
    },
    $isError: 1
  };
  A._Exception.prototype = {
    toString$0(_) {
      return "Exception: " + this.message;
    }
  };
  A.FormatException.prototype = {
    toString$0(_) {
      var lineEnd, lineNum, lineStart, previousCharWasCR, i, char, prefix, postfix, end, start,
        message = this.message,
        report = "" !== message ? "FormatException: " + message : "FormatException",
        offset = this.offset,
        source = this.source,
        t1 = offset < 0 || offset > source.length;
      if (t1)
        offset = null;
      if (offset == null) {
        if (source.length > 78)
          source = B.JSString_methods.substring$2(source, 0, 75) + "...";
        return report + "\n" + source;
      }
      for (lineEnd = source.length, lineNum = 1, lineStart = 0, previousCharWasCR = false, i = 0; i < offset; ++i) {
        if (!(i < lineEnd))
          return A.ioore(source, i);
        char = source.charCodeAt(i);
        if (char === 10) {
          if (lineStart !== i || !previousCharWasCR)
            ++lineNum;
          lineStart = i + 1;
          previousCharWasCR = false;
        } else if (char === 13) {
          ++lineNum;
          lineStart = i + 1;
          previousCharWasCR = true;
        }
      }
      report = lineNum > 1 ? report + (" (at line " + lineNum + ", character " + (offset - lineStart + 1) + ")\n") : report + (" (at character " + (offset + 1) + ")\n");
      for (i = offset; i < lineEnd; ++i) {
        if (!(i >= 0))
          return A.ioore(source, i);
        char = source.charCodeAt(i);
        if (char === 10 || char === 13) {
          lineEnd = i;
          break;
        }
      }
      prefix = "";
      if (lineEnd - lineStart > 78) {
        postfix = "...";
        if (offset - lineStart < 75) {
          end = lineStart + 75;
          start = lineStart;
        } else {
          if (lineEnd - offset < 75) {
            start = lineEnd - 75;
            end = lineEnd;
            postfix = "";
          } else {
            start = offset - 36;
            end = offset + 36;
          }
          prefix = "...";
        }
      } else {
        end = lineEnd;
        start = lineStart;
        postfix = "";
      }
      return report + prefix + B.JSString_methods.substring$2(source, start, end) + postfix + "\n" + B.JSString_methods.$mul(" ", offset - start + prefix.length) + "^\n";
    }
  };
  A.Iterable.prototype = {
    map$1$1(_, toElement, $T) {
      var t1 = A._instanceType(this);
      return A.MappedIterable_MappedIterable(this, t1._bind$1($T)._eval$1("1(Iterable.E)")._as(toElement), t1._eval$1("Iterable.E"), $T);
    },
    get$length(_) {
      var count,
        it = this.get$iterator(this);
      for (count = 0; it.moveNext$0();)
        ++count;
      return count;
    },
    elementAt$1(_, index) {
      var iterator, skipCount;
      A.RangeError_checkNotNegative(index, "index");
      iterator = this.get$iterator(this);
      for (skipCount = index; iterator.moveNext$0();) {
        if (skipCount === 0)
          return iterator.get$current();
        --skipCount;
      }
      throw A.wrapException(A.IndexError$withLength(index, index - skipCount, this, "index"));
    },
    toString$0(_) {
      return A.Iterable_iterableToShortString(this, "(", ")");
    }
  };
  A.Null.prototype = {
    get$hashCode(_) {
      return A.Object.prototype.get$hashCode.call(this, 0);
    },
    toString$0(_) {
      return "null";
    }
  };
  A.Object.prototype = {$isObject: 1,
    $eq(_, other) {
      return this === other;
    },
    get$hashCode(_) {
      return A.Primitives_objectHashCode(this);
    },
    toString$0(_) {
      return "Instance of '" + A.Primitives_objectTypeName(this) + "'";
    },
    noSuchMethod$1(_, invocation) {
      throw A.wrapException(A.NoSuchMethodError_NoSuchMethodError$withInvocation(this, type$.Invocation._as(invocation)));
    },
    get$runtimeType(_) {
      return A.getRuntimeTypeOfDartObject(this);
    },
    toString() {
      return this.toString$0(this);
    }
  };
  A._StringStackTrace.prototype = {
    toString$0(_) {
      return "";
    },
    $isStackTrace: 1
  };
  A.StringBuffer.prototype = {
    get$length(_) {
      return this._contents.length;
    },
    toString$0(_) {
      var t1 = this._contents;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    }
  };
  A.jsify__convert.prototype = {
    call$1(o) {
      var t1, convertedMap, key, convertedList;
      if (A._noJsifyRequired(o))
        return o;
      t1 = this._convertedObjects;
      if (t1.containsKey$1(o))
        return t1.$index(0, o);
      if (type$.Map_of_nullable_Object_and_nullable_Object._is(o)) {
        convertedMap = {};
        t1.$indexSet(0, o, convertedMap);
        for (t1 = o.get$keys(), t1 = t1.get$iterator(t1); t1.moveNext$0();) {
          key = t1.get$current();
          convertedMap[key] = this.call$1(o.$index(0, key));
        }
        return convertedMap;
      } else if (type$.Iterable_nullable_Object._is(o)) {
        convertedList = [];
        t1.$indexSet(0, o, convertedList);
        B.JSArray_methods.addAll$1(convertedList, J.map$1$1$ax(o, this, type$.dynamic));
        return convertedList;
      } else
        return o;
    },
    $signature: 8
  };
  A.promiseToFuture_closure.prototype = {
    call$1(r) {
      return this.completer.complete$1(this.T._eval$1("0/?")._as(r));
    },
    $signature: 3
  };
  A.promiseToFuture_closure0.prototype = {
    call$1(e) {
      if (e == null)
        return this.completer.completeError$1(new A.NullRejectionException(e === undefined));
      return this.completer.completeError$1(e);
    },
    $signature: 3
  };
  A.dartify_convert.prototype = {
    call$1(o) {
      var t1, millisSinceEpoch, proto, t2, dartObject, originalKeys, dartKeys, i, jsKey, dartKey, l, $length;
      if (A._noDartifyRequired(o))
        return o;
      t1 = this._convertedObjects;
      o.toString;
      if (t1.containsKey$1(o))
        return t1.$index(0, o);
      if (o instanceof Date) {
        millisSinceEpoch = o.getTime();
        if (millisSinceEpoch < -864e13 || millisSinceEpoch > 864e13)
          A.throwExpression(A.RangeError$range(millisSinceEpoch, -864e13, 864e13, "millisecondsSinceEpoch", null));
        A.checkNotNullable(true, "isUtc", type$.bool);
        return new A.DateTime(millisSinceEpoch, 0, true);
      }
      if (o instanceof RegExp)
        throw A.wrapException(A.ArgumentError$("structured clone of RegExp", null));
      if (typeof Promise != "undefined" && o instanceof Promise)
        return A.promiseToFuture(o, type$.nullable_Object);
      proto = Object.getPrototypeOf(o);
      if (proto === Object.prototype || proto === null) {
        t2 = type$.nullable_Object;
        dartObject = A.LinkedHashMap_LinkedHashMap$_empty(t2, t2);
        t1.$indexSet(0, o, dartObject);
        originalKeys = Object.keys(o);
        dartKeys = [];
        for (t1 = J.getInterceptor$ax(originalKeys), t2 = t1.get$iterator(originalKeys); t2.moveNext$0();)
          dartKeys.push(A.dartify(t2.get$current()));
        for (i = 0; i < t1.get$length(originalKeys); ++i) {
          jsKey = t1.$index(originalKeys, i);
          if (!(i < dartKeys.length))
            return A.ioore(dartKeys, i);
          dartKey = dartKeys[i];
          if (jsKey != null)
            dartObject.$indexSet(0, dartKey, this.call$1(o[jsKey]));
        }
        return dartObject;
      }
      if (o instanceof Array) {
        l = o;
        dartObject = [];
        t1.$indexSet(0, o, dartObject);
        $length = A._asInt(o.length);
        for (t1 = J.getInterceptor$asx(l), i = 0; i < $length; ++i)
          dartObject.push(this.call$1(t1.$index(l, i)));
        return dartObject;
      }
      return o;
    },
    $signature: 8
  };
  A.NullRejectionException.prototype = {
    toString$0(_) {
      return "Promise was rejected with a value of `" + (this.isUndefined ? "undefined" : "null") + "`.";
    }
  };
  A._JSSecureRandom.prototype = {
    _JSSecureRandom$0() {
      var $crypto = self.crypto;
      if ($crypto != null)
        if ($crypto.getRandomValues != null)
          return;
      throw A.wrapException(A.UnsupportedError$("No source of cryptographically secure random numbers available."));
    },
    nextInt$1(max) {
      var byteCount, t1, start, randomLimit, t2, t3, random, result, _null = null;
      if (max <= 0 || max > 4294967296)
        throw A.wrapException(new A.RangeError(_null, _null, false, _null, _null, "max must be in range 0 < max \u2264 2^32, was " + max));
      if (max > 255)
        if (max > 65535)
          byteCount = max > 16777215 ? 4 : 3;
        else
          byteCount = 2;
      else
        byteCount = 1;
      t1 = this._math$_buffer;
      t1.$flags & 2 && A.throwUnsupportedOperation(t1, 11);
      t1.setUint32(0, 0, false);
      start = 4 - byteCount;
      randomLimit = A._asInt(Math.pow(256, byteCount));
      for (t2 = max - 1, t3 = (max & t2) === 0; true;) {
        crypto.getRandomValues(J.asUint8List$2$x(B.NativeByteData_methods.get$buffer(t1), start, byteCount));
        random = t1.getUint32(0, false);
        if (t3)
          return (random & t2) >>> 0;
        result = random % max;
        if (random - result + max < randomLimit)
          return result;
      }
    }
  };
  A.Level.prototype = {
    $eq(_, other) {
      if (other == null)
        return false;
      return other instanceof A.Level && this.value === other.value;
    },
    get$hashCode(_) {
      return this.value;
    },
    toString$0(_) {
      return this.name;
    }
  };
  A.LogRecord.prototype = {
    toString$0(_) {
      return "[" + this.level.name + "] " + this.loggerName + ": " + this.message;
    }
  };
  A.Logger.prototype = {
    get$fullName() {
      var t1 = this.parent,
        t2 = t1 == null ? null : t1.name.length !== 0,
        t3 = this.name;
      return t2 === true ? t1.get$fullName() + "." + t3 : t3;
    },
    get$level() {
      var t1, effectiveLevel;
      if (this.parent == null) {
        t1 = this._level;
        t1.toString;
        effectiveLevel = t1;
      } else {
        t1 = $.$get$Logger_root()._level;
        t1.toString;
        effectiveLevel = t1;
      }
      return effectiveLevel;
    },
    log$4(logLevel, message, error, stackTrace) {
      var record, _this = this,
        t1 = logLevel.value;
      if (t1 >= _this.get$level().value) {
        if (t1 >= 2000) {
          A.StackTrace_current();
          logLevel.toString$0(0);
        }
        t1 = _this.get$fullName();
        Date.now();
        $.LogRecord__nextNumber = $.LogRecord__nextNumber + 1;
        record = new A.LogRecord(logLevel, message, t1);
        if (_this.parent == null)
          _this._publish$1(record);
        else
          $.$get$Logger_root()._publish$1(record);
      }
    },
    _getStream$0() {
      if (this.parent == null) {
        var t1 = this._controller;
        if (t1 == null) {
          t1 = new A._SyncBroadcastStreamController(null, null, type$._SyncBroadcastStreamController_LogRecord);
          this.set$_controller(t1);
        }
        return new A._BroadcastStream(t1, A._instanceType(t1)._eval$1("_BroadcastStream<1>"));
      } else
        return $.$get$Logger_root()._getStream$0();
    },
    _publish$1(record) {
      var t1 = this._controller;
      if (t1 != null) {
        A._instanceType(t1)._precomputed1._as(record);
        if (!t1.get$_mayAddEvent())
          A.throwExpression(t1._addEventError$0());
        t1._sendData$1(record);
      }
      return null;
    },
    set$_controller(_controller) {
      this._controller = type$.nullable_StreamController_LogRecord._as(_controller);
    }
  };
  A.Logger_Logger_closure.prototype = {
    call$0() {
      var dot, $parent, t1,
        thisName = this.name;
      if (B.JSString_methods.startsWith$1(thisName, "."))
        A.throwExpression(A.ArgumentError$("name shouldn't start with a '.'", null));
      if (B.JSString_methods.endsWith$1(thisName, "."))
        A.throwExpression(A.ArgumentError$("name shouldn't end with a '.'", null));
      dot = B.JSString_methods.lastIndexOf$1(thisName, ".");
      if (dot === -1)
        $parent = thisName !== "" ? A.Logger_Logger("") : null;
      else {
        $parent = A.Logger_Logger(B.JSString_methods.substring$2(thisName, 0, dot));
        thisName = B.JSString_methods.substring$1(thisName, dot + 1);
      }
      t1 = new A.Logger(thisName, $parent, A.LinkedHashMap_LinkedHashMap$_empty(type$.String, type$.Logger));
      if ($parent == null)
        t1._level = B.Level_INFO_800;
      else
        $parent._children.$indexSet(0, thisName, t1);
      return t1;
    },
    $signature: 21
  };
  A.CryptorError.prototype = {
    _enumToString$0() {
      return "CryptorError." + this._name;
    }
  };
  A.FrameInfo.prototype = {};
  A.FrameCryptor.prototype = {
    get$enabled() {
      if (this.participantIdentity == null)
        return false;
      return this._enabled;
    },
    setupTransform$6$codec$kind$operation$readable$trackId$writable(codec, kind, operation, readable, trackId, writable) {
      return this.setupTransform$body$FrameCryptor(codec, kind, operation, readable, trackId, writable);
    },
    setupTransform$5$kind$operation$readable$trackId$writable(kind, operation, readable, trackId, writable) {
      return this.setupTransform$6$codec$kind$operation$readable$trackId$writable(null, kind, operation, readable, trackId, writable);
    },
    setupTransform$body$FrameCryptor(codec, kind, operation, readable, trackId, writable) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this, transformer, e, t2, t3, t4, t5, exception, t1;
      var $async$setupTransform$6$codec$kind$operation$readable$trackId$writable = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $.$get$logger();
              t1.log$4(B.Level_INFO_800, "setupTransform " + operation + " kind " + kind, null, null);
              $async$self.__FrameCryptor_kind_A = kind;
              if (codec != null) {
                t1.log$4(B.Level_INFO_800, "setting codec on cryptor to " + codec, null, null);
                $async$self.codec = codec;
              }
              t1 = self.TransformStream;
              t2 = operation === "encode" ? $async$self.get$encodeFunction() : $async$self.get$decodeFunction();
              t3 = type$.Future_void_Function_JSObject_JSObject;
              t4 = type$.String;
              t5 = type$.JSObject;
              transformer = t5._as(new t1(t5._as(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["transform", A.allowInterop(t2, t3)], t4, t3)))));
              try {
                t5._as(t5._as(readable.pipeThrough(transformer)).pipeTo(writable));
              } catch (exception) {
                e = A.unwrapException(exception);
                $.$get$logger().log$4(B.Level_WARNING_900, "e " + J.toString$0$(e), null, null);
                if ($async$self.lastError !== B.CryptorError_7) {
                  $async$self.lastError = B.CryptorError_7;
                  $async$self.worker.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", $async$self.participantIdentity, "state", "internalError", "error", "Internal error: " + J.toString$0$(e)], t4, type$.nullable_String)));
                }
              }
              $async$self.trackId = trackId;
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$setupTransform$6$codec$kind$operation$readable$trackId$writable, $async$completer);
    },
    getUnencryptedBytes$2(obj, codec) {
      var naluIndices, t1, t2, _i, index, type, _null = null, frameType = "",
        data = A.NativeUint8List_NativeUint8List$view(type$.NativeByteBuffer._as(obj.data), 0, _null);
      if ("type" in obj) {
        frameType = A._asString(obj.type);
        $.$get$logger().log$4(B.Level_FINER_400, "frameType: " + frameType, _null, _null);
      }
      if (codec != null && codec.toLowerCase() === "h264") {
        type$.Uint8List._as(data);
        naluIndices = A.findNALUIndices(data);
        for (t1 = naluIndices.length, t2 = data.length, _i = 0; _i < naluIndices.length; naluIndices.length === t1 || (0, A.throwConcurrentModificationError)(naluIndices), ++_i) {
          index = naluIndices[_i];
          if (!(index < t2))
            return A.ioore(data, index);
          type = data[index] & 31;
          switch (type) {
            case 5:
            case 1:
              t1 = index + 2;
              $.$get$logger().log$4(B.Level_FINER_400, "unEncryptedBytes NALU of type " + type + ", offset " + t1, _null, _null);
              return t1;
            default:
              $.$get$logger().log$4(B.Level_FINER_400, "skipping NALU of type " + type, _null, _null);
              break;
          }
        }
        throw A.wrapException(A.Exception_Exception("Could not find NALU"));
      }
      switch (frameType) {
        case "key":
          return 10;
        case "delta":
          return 3;
        case "audio":
          return 1;
        default:
          return 0;
      }
    },
    readFrameInfo$1(frameObj) {
      var buffer, frameType, t1, synchronizationSource, timestamp;
      new Uint8Array(0);
      buffer = A.NativeUint8List_NativeUint8List$view(type$.NativeByteBuffer._as(frameObj.data), 0, null);
      if ("type" in frameObj) {
        frameType = A._asString(frameObj.type);
        $.$get$logger().log$4(B.Level_FINER_400, "frameType: " + frameType, null, null);
      } else
        frameType = "";
      t1 = type$.JSObject;
      synchronizationSource = A._asInt(t1._as(frameObj.getMetadata()).synchronizationSource);
      if ("rtpTimestamp" in t1._as(frameObj.getMetadata()))
        timestamp = B.JSInt_methods.toInt$0(A._asInt(t1._as(frameObj.getMetadata()).rtpTimestamp));
      else
        timestamp = "timestamp" in frameObj ? A._asInt(A._asDouble(frameObj.timestamp)) : 0;
      return new A.FrameInfo(frameType, synchronizationSource, timestamp, buffer);
    },
    enqueueFrame$3(frameObj, controller, buffer) {
      var t1 = type$.NativeByteBuffer._as(B.NativeUint8List_methods.get$buffer(buffer.toBytes$0()));
      frameObj.data = t1;
      controller.enqueue(frameObj);
    },
    encodeFunction$2(frameObj, controller) {
      var t1 = type$.JSObject;
      return this.encodeFunction$body$FrameCryptor(t1._as(frameObj), t1._as(controller));
    },
    encodeFunction$body$FrameCryptor(frameObj, controller) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$returnValue, $async$handler = 2, $async$errorStack = [], $async$self = this, srcFrame, secretKey, keyIndex, headerLength, iv, frameTrailer, cipherText, finalBuffer, e, t1, t2, t3, t4, iv0, sendCount, t5, t6, t7, exception, $async$exception, $async$temp1;
      var $async$encodeFunction$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1) {
          $async$errorStack.push($async$result);
          $async$goto = $async$handler;
        }
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              $async$handler = 4;
              t1 = true;
              if ($async$self.get$enabled()) {
                t2 = type$.NativeByteBuffer;
                if (!(t2._as(frameObj.data).byteLength === 0))
                  t1 = t2._as(frameObj.data).byteLength === 0;
              }
              if (t1) {
                if ($async$self.keyHandler.keyOptions.discardFrameWhenCryptorNotReady) {
                  // goto return
                  $async$goto = 1;
                  break;
                }
                controller.enqueue(frameObj);
                // goto return
                $async$goto = 1;
                break;
              }
              srcFrame = $async$self.readFrameInfo$1(frameObj);
              t1 = $.$get$logger();
              t1.log$4(B.Level_FINE_500, "encodeFunction: buffer " + srcFrame.buffer.length + ", synchronizationSource " + srcFrame.ssrc + " frameType " + srcFrame.frameType, null, null);
              t2 = $async$self.keyHandler.getKeySet$1($async$self.currentKeyIndex);
              secretKey = t2 == null ? null : t2.encryptionKey;
              keyIndex = $async$self.currentKeyIndex;
              if (secretKey == null) {
                if ($async$self.lastError !== B.CryptorError_5) {
                  $async$self.lastError = B.CryptorError_5;
                  t1 = $async$self.participantIdentity;
                  t2 = $async$self.trackId;
                  t3 = $async$self.__FrameCryptor_kind_A;
                  t3 === $ && A.throwLateFieldNI("kind");
                  $async$self.worker.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", t1, "trackId", t2, "kind", t3, "state", "missingKey", "error", "Missing key for track " + t2], type$.String, type$.nullable_String)));
                }
                // goto return
                $async$goto = 1;
                break;
              }
              t2 = $async$self.__FrameCryptor_kind_A;
              t2 === $ && A.throwLateFieldNI("kind");
              headerLength = t2 === "video" ? $async$self.getUnencryptedBytes$2(frameObj, $async$self.codec) : 1;
              t3 = srcFrame.ssrc;
              t4 = srcFrame.timestamp;
              iv0 = new DataView(new ArrayBuffer(12));
              t2 = $async$self.sendCounts;
              if (t2.$index(0, t3) == null)
                t2.$indexSet(0, t3, $.$get$Random__secureRandom().nextInt$1(65535));
              sendCount = t2.$index(0, t3);
              if (sendCount == null)
                sendCount = 0;
              iv0.setUint32(0, t3, false);
              iv0.setUint32(4, t4, false);
              iv0.setUint32(8, t4 - B.JSInt_methods.$mod(sendCount, 65535), false);
              t2.$indexSet(0, t3, sendCount + 1);
              iv = J.asUint8List$0$x(B.NativeByteData_methods.get$buffer(iv0));
              frameTrailer = new DataView(new ArrayBuffer(2));
              t2 = frameTrailer;
              t2.$flags & 2 && A.throwUnsupportedOperation(t2, 6);
              J._setInt8$2$x(t2, 0, 12);
              t2 = frameTrailer;
              t3 = A._asInt(keyIndex);
              t2.$flags & 2 && A.throwUnsupportedOperation(t2, 6);
              J._setInt8$2$x(t2, 1, t3);
              t3 = $async$self.worker;
              t2 = type$.JSObject;
              t4 = t2._as(t2._as(t3.crypto).subtle);
              t5 = type$.String;
              t6 = type$.Object;
              t7 = A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["name", "AES-GCM", "iv", iv, "additionalData", B.NativeUint8List_methods.sublist$2(srcFrame.buffer, 0, headerLength)], t5, t6));
              t6 = t7 == null ? t6._as(t7) : t7;
              $async$temp1 = type$.NativeByteBuffer;
              $async$goto = 7;
              return A._asyncAwait(A.promiseToFuture(t2._as(t4.encrypt(t6, secretKey, B.NativeUint8List_methods.sublist$2(srcFrame.buffer, headerLength, srcFrame.buffer.length))), type$.nullable_Object), $async$encodeFunction$2);
            case 7:
              // returning from await.
              cipherText = $async$temp1._as($async$result);
              t1.log$4(B.Level_FINER_400, "encodeFunction: encrypted buffer: " + srcFrame.buffer.length + ", cipherText: " + A.NativeUint8List_NativeUint8List$view(cipherText, 0, null).length, null, null);
              t2 = $.$get$_CopyingBytesBuilder__emptyList();
              finalBuffer = new A._CopyingBytesBuilder(t2);
              J.add$1$ax(finalBuffer, new Uint8Array(A._ensureNativeList(B.NativeUint8List_methods.sublist$2(srcFrame.buffer, 0, headerLength))));
              J.add$1$ax(finalBuffer, A.NativeUint8List_NativeUint8List$view(cipherText, 0, null));
              J.add$1$ax(finalBuffer, iv);
              J.add$1$ax(finalBuffer, J.asUint8List$0$x(J.get$buffer$x(frameTrailer)));
              $async$self.enqueueFrame$3(frameObj, controller, finalBuffer);
              if ($async$self.lastError !== B.CryptorError_1) {
                $async$self.lastError = B.CryptorError_1;
                t3.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", $async$self.participantIdentity, "trackId", $async$self.trackId, "kind", $async$self.__FrameCryptor_kind_A, "state", "ok", "error", "encryption ok"], t5, type$.nullable_String)));
              }
              t1.log$4(B.Level_FINER_400, "encodeFunction[CryptorError.kOk]: frame enqueued kind " + $async$self.__FrameCryptor_kind_A + ",codec " + A.S($async$self.codec) + " headerLength: " + A.S(headerLength) + ",  timestamp: " + srcFrame.timestamp + ", ssrc: " + srcFrame.ssrc + ", data length: " + srcFrame.buffer.length + ", encrypted length: " + finalBuffer.toBytes$0().length + ", iv " + A.S(iv), null, null);
              $async$handler = 2;
              // goto after finally
              $async$goto = 6;
              break;
            case 4:
              // catch
              $async$handler = 3;
              $async$exception = $async$errorStack.pop();
              e = A.unwrapException($async$exception);
              $.$get$logger().log$4(B.Level_WARNING_900, "encodeFunction encrypt: e " + J.toString$0$(e), null, null);
              if ($async$self.lastError !== B.CryptorError_3) {
                $async$self.lastError = B.CryptorError_3;
                t1 = $async$self.participantIdentity;
                t2 = $async$self.trackId;
                t3 = $async$self.__FrameCryptor_kind_A;
                t3 === $ && A.throwLateFieldNI("kind");
                $async$self.worker.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", t1, "trackId", t2, "kind", t3, "state", "encryptError", "error", J.toString$0$(e)], type$.String, type$.nullable_String)));
              }
              // goto after finally
              $async$goto = 6;
              break;
            case 3:
              // uncaught
              // goto rethrow
              $async$goto = 2;
              break;
            case 6:
              // after finally
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
            case 2:
              // rethrow
              return A._asyncRethrow($async$errorStack.at(-1), $async$completer);
          }
      });
      return A._asyncStartSync($async$encodeFunction$2, $async$completer);
    },
    decodeFunction$2(frameObj, controller) {
      var t1 = type$.JSObject;
      return this.decodeFunction$body$FrameCryptor(t1._as(frameObj), t1._as(controller));
    },
    decodeFunction$body$FrameCryptor(frameObj, controller) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$returnValue, $async$handler = 2, $async$errorStack = [], $async$self = this, headerLength, frameTrailer, ivLength, keyIndex, iv, decryptFrameInternal, ratchedKeyInternal, e, finalBuffer, e0, t2, t3, t4, t5, t6, magicBytesBuffer, t7, initialKeySet, exception, t1, srcFrame, $async$exception, $async$exception1;
      var $async$decodeFunction$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1) {
          $async$errorStack.push($async$result);
          $async$goto = $async$handler;
        }
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = {};
              srcFrame = $async$self.readFrameInfo$1(frameObj);
              t1.ratchetCount = 0;
              t2 = $.$get$logger();
              t2.log$4(B.Level_FINE_500, "decodeFunction: frame lenght " + srcFrame.buffer.length, null, null);
              t1.initialKeySet = t1.decrypted = null;
              t1.initialKeyIndex = $async$self.currentKeyIndex;
              if (!$async$self.get$enabled() || srcFrame.buffer.length === 0) {
                $async$self.sifGuard.recordUserFrame$0();
                if ($async$self.keyHandler.keyOptions.discardFrameWhenCryptorNotReady) {
                  // goto return
                  $async$goto = 1;
                  break;
                }
                t2.log$4(B.Level_FINE_500, "enqueing empty frame", null, null);
                controller.enqueue(frameObj);
                t2.log$4(B.Level_FINER_400, "enqueing silent frame", null, null);
                // goto return
                $async$goto = 1;
                break;
              }
              t3 = $async$self.keyHandler.keyOptions.uncryptedMagicBytes;
              if (t3 != null) {
                t4 = srcFrame.buffer;
                t5 = t3.length;
                t6 = t5 + 1;
                if (t4.length > t6) {
                  magicBytesBuffer = B.NativeUint8List_methods.sublist$2(srcFrame.buffer, srcFrame.buffer.length - t5 - 1, srcFrame.buffer.length - 1);
                  t2.log$4(B.Level_FINER_400, "magicBytesBuffer " + A.S(magicBytesBuffer) + ", magicBytes " + A.S(t3), null, null);
                  t4 = $async$self.sifGuard;
                  if (A.Iterable_iterableToFullString(magicBytesBuffer, "[", "]") === A.Iterable_iterableToFullString(t3, "[", "]")) {
                    ++t4.consecutiveSifCount;
                    if (t4.sifSequenceStartedAt == null)
                      t4.sifSequenceStartedAt = Date.now();
                    t4.lastSifReceivedAt = Date.now();
                    if (t4.consecutiveSifCount < 100)
                      if (t4.sifSequenceStartedAt != null) {
                        t1 = Date.now();
                        t4 = t4.sifSequenceStartedAt;
                        t4.toString;
                        t4 = t1 - t4 < 2000;
                        t1 = t4;
                      } else
                        t1 = true;
                    else
                      t1 = false;
                    if (t1) {
                      t1 = B.NativeUint8List_methods.sublist$1(srcFrame.buffer, srcFrame.buffer.length - 1);
                      if (0 >= t1.length) {
                        $async$returnValue = A.ioore(t1, 0);
                        // goto return
                        $async$goto = 1;
                        break;
                      }
                      t2.log$4(B.Level_FINER_400, "ecodeFunction: skip uncrypted frame, type " + t1[0], null, null);
                      finalBuffer = new A._CopyingBytesBuilder($.$get$_CopyingBytesBuilder__emptyList());
                      finalBuffer.add$1(0, new Uint8Array(A._ensureNativeList(B.NativeUint8List_methods.sublist$2(srcFrame.buffer, 0, srcFrame.buffer.length - t6))));
                      $async$self.enqueueFrame$3(frameObj, controller, finalBuffer);
                      t2.log$4(B.Level_FINE_500, "ecodeFunction: enqueing silent frame", null, null);
                      controller.enqueue(frameObj);
                    } else
                      t2.log$4(B.Level_FINER_400, "ecodeFunction: SIF limit reached, dropping frame", null, null);
                    t2.log$4(B.Level_FINER_400, "ecodeFunction: enqueing silent frame", null, null);
                    controller.enqueue(frameObj);
                    // goto return
                    $async$goto = 1;
                    break;
                  } else
                    t4.recordUserFrame$0();
                }
              }
              $async$handler = 4;
              t3 = {};
              t4 = $async$self.__FrameCryptor_kind_A;
              t4 === $ && A.throwLateFieldNI("kind");
              headerLength = t4 === "video" ? $async$self.getUnencryptedBytes$2(frameObj, $async$self.codec) : 1;
              frameTrailer = B.NativeUint8List_methods.sublist$1(srcFrame.buffer, srcFrame.buffer.length - 2);
              ivLength = J.$index$asx(frameTrailer, 0);
              keyIndex = J.$index$asx(frameTrailer, 1);
              t5 = srcFrame.buffer;
              t6 = srcFrame.buffer;
              t7 = ivLength;
              if (typeof t7 !== "number") {
                $async$returnValue = A.iae(t7);
                // goto return
                $async$goto = 1;
                break;
              }
              iv = B.NativeUint8List_methods.sublist$2(t5, t6.length - t7 - 2, srcFrame.buffer.length - 2);
              initialKeySet = t1.initialKeySet = $async$self.keyHandler.getKeySet$1(keyIndex);
              t1.initialKeyIndex = keyIndex;
              t2.log$4(B.Level_FINER_400, "decodeFunction: start decrypting frame headerLength " + A.S(headerLength) + " " + srcFrame.buffer.length + " frameTrailer " + A.S(frameTrailer) + ", ivLength " + A.S(ivLength) + ", keyIndex " + A.S(keyIndex) + ", iv " + A.S(iv), null, null);
              if (initialKeySet == null || !$async$self.keyHandler._hasValidKey) {
                if ($async$self.lastError !== B.CryptorError_5) {
                  $async$self.lastError = B.CryptorError_5;
                  t1 = $async$self.participantIdentity;
                  t2 = $async$self.trackId;
                  $async$self.worker.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", t1, "trackId", t2, "kind", $async$self.__FrameCryptor_kind_A, "state", "missingKey", "error", "Missing key for track " + t2], type$.String, type$.nullable_String)));
                }
                // goto return
                $async$goto = 1;
                break;
              }
              t3.currentkeySet = initialKeySet;
              decryptFrameInternal = new A.FrameCryptor_decodeFunction_decryptFrameInternal(t1, t3, $async$self, iv, srcFrame, headerLength, ivLength);
              ratchedKeyInternal = new A.FrameCryptor_decodeFunction_ratchedKeyInternal(t1, t3, $async$self, decryptFrameInternal);
              $async$handler = 8;
              $async$goto = 11;
              return A._asyncAwait(decryptFrameInternal.call$0(), $async$decodeFunction$2);
            case 11:
              // returning from await.
              $async$handler = 4;
              // goto after finally
              $async$goto = 10;
              break;
            case 8:
              // catch
              $async$handler = 7;
              $async$exception = $async$errorStack.pop();
              e = A.unwrapException($async$exception);
              $async$self.lastError = B.CryptorError_7;
              t2 = $.$get$logger();
              t2.log$4(B.Level_FINER_400, "decodeFunction: kInternalError catch " + A.S(e), null, null);
              $async$goto = 12;
              return A._asyncAwait(ratchedKeyInternal.call$0(), $async$decodeFunction$2);
            case 12:
              // returning from await.
              // goto after finally
              $async$goto = 10;
              break;
            case 7:
              // uncaught
              // goto catch
              $async$goto = 4;
              break;
            case 10:
              // after finally
              t3 = t1.decrypted;
              if (t3 == null) {
                t1 = A.Exception_Exception("[decodeFunction] decryption failed even after ratchting");
                throw A.wrapException(t1);
              }
              t4 = $async$self.keyHandler;
              t4._decryptionFailureCount = 0;
              t4._hasValidKey = true;
              t2.log$4(B.Level_FINER_400, "decodeFunction: decryption success, buffer length " + srcFrame.buffer.length + ", decrypted: " + A.NativeUint8List_NativeUint8List$view(t3, 0, null).length, null, null);
              t3 = $.$get$_CopyingBytesBuilder__emptyList();
              finalBuffer = new A._CopyingBytesBuilder(t3);
              J.add$1$ax(finalBuffer, new Uint8Array(A._ensureNativeList(B.NativeUint8List_methods.sublist$2(srcFrame.buffer, 0, headerLength))));
              t1 = t1.decrypted;
              t1.toString;
              J.add$1$ax(finalBuffer, A.NativeUint8List_NativeUint8List$view(t1, 0, null));
              $async$self.enqueueFrame$3(frameObj, controller, finalBuffer);
              if ($async$self.lastError !== B.CryptorError_1) {
                $async$self.lastError = B.CryptorError_1;
                $async$self.worker.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", $async$self.participantIdentity, "trackId", $async$self.trackId, "kind", $async$self.__FrameCryptor_kind_A, "state", "ok", "error", "decryption ok"], type$.String, type$.nullable_String)));
              }
              t2.log$4(B.Level_FINE_500, "decodeFunction[CryptorError.kOk]: decryption success kind " + $async$self.__FrameCryptor_kind_A + ", headerLength: " + A.S(headerLength) + ", timestamp: " + srcFrame.timestamp + ", ssrc: " + srcFrame.ssrc + ", data length: " + srcFrame.buffer.length + ", decrypted length: " + finalBuffer.toBytes$0().length + ", keyindex " + A.S(keyIndex) + " iv " + A.S(iv), null, null);
              $async$handler = 2;
              // goto after finally
              $async$goto = 6;
              break;
            case 4:
              // catch
              $async$handler = 3;
              $async$exception1 = $async$errorStack.pop();
              e0 = A.unwrapException($async$exception1);
              if ($async$self.lastError !== B.CryptorError_2) {
                $async$self.lastError = B.CryptorError_2;
                t1 = $async$self.participantIdentity;
                t2 = $async$self.trackId;
                t3 = $async$self.__FrameCryptor_kind_A;
                t3 === $ && A.throwLateFieldNI("kind");
                $async$self.worker.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", t1, "trackId", t2, "kind", t3, "state", "decryptError", "error", J.toString$0$(e0)], type$.String, type$.nullable_String)));
              }
              $async$self.keyHandler.decryptionFailure$0();
              // goto after finally
              $async$goto = 6;
              break;
            case 3:
              // uncaught
              // goto rethrow
              $async$goto = 2;
              break;
            case 6:
              // after finally
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
            case 2:
              // rethrow
              return A._asyncRethrow($async$errorStack.at(-1), $async$completer);
          }
      });
      return A._asyncStartSync($async$decodeFunction$2, $async$completer);
    }
  };
  A.FrameCryptor_decodeFunction_decryptFrameInternal.prototype = {
    call$0() {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this, decrypted, t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, $async$temp1;
      var $async$call$0 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $async$self.$this;
              t2 = t1.worker;
              t3 = type$.JSObject;
              t4 = t3._as(t3._as(t2.crypto).subtle);
              t5 = $async$self.srcFrame;
              t6 = t5.buffer;
              t7 = $async$self.headerLength;
              t8 = type$.String;
              t9 = type$.Object;
              t10 = A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["name", "AES-GCM", "iv", $async$self.iv, "additionalData", B.NativeUint8List_methods.sublist$2(t6, 0, t7)], t8, t9));
              t9 = t10 == null ? t9._as(t10) : t10;
              t10 = $async$self._box_0;
              $async$temp1 = type$.NativeByteBuffer;
              $async$goto = 2;
              return A._asyncAwait(A.promiseToFuture(t3._as(t4.decrypt(t9, t10.currentkeySet.encryptionKey, B.NativeUint8List_methods.sublist$2(t6, t7, t6.length - $async$self.ivLength - 2))), type$.nullable_Object), $async$call$0);
            case 2:
              // returning from await.
              decrypted = $async$temp1._as($async$result);
              t6 = $async$self._box_1;
              t6.decrypted = decrypted;
              t7 = $.$get$logger();
              t7.log$4(B.Level_FINER_400, string$.decode + A.NativeUint8List_NativeUint8List$view(decrypted, 0, null).length, null, null);
              t3 = t6.decrypted;
              if (t3 == null)
                throw A.wrapException(A.Exception_Exception("[decryptFrameInternal] could not decrypt"));
              t7.log$4(B.Level_FINER_400, string$.decode + A.NativeUint8List_NativeUint8List$view(t3, 0, null).length, null, null);
              $async$goto = t10.currentkeySet !== t6.initialKeySet ? 3 : 4;
              break;
            case 3:
              // then
              t7.log$4(B.Level_FINE_500, "decodeFunction::decryptFrameInternal: ratchetKey: decryption ok, newState: kKeyRatcheted", null, null);
              $async$goto = 5;
              return A._asyncAwait(t1.keyHandler.setKeySetFromMaterial$2(t10.currentkeySet, t6.initialKeyIndex), $async$call$0);
            case 5:
              // returning from await.
            case 4:
              // join
              t3 = t1.lastError;
              if (t3 !== B.CryptorError_1 && t3 !== B.CryptorError_6 && t6.ratchetCount > 0) {
                t7.log$4(B.Level_FINER_400, "decodeFunction::decryptFrameInternal: KeyRatcheted: ssrc " + t5.ssrc + " timestamp " + t5.timestamp + " ratchetCount " + t6.ratchetCount + "  participantId: " + A.S(t1.participantIdentity), null, null);
                t7.log$4(B.Level_FINER_400, "decodeFunction::decryptFrameInternal: ratchetKey: lastError != CryptorError.kKeyRatcheted, reset state to kKeyRatcheted", null, null);
                t1.lastError = B.CryptorError_6;
                t3 = t1.participantIdentity;
                t4 = t1.trackId;
                t1 = t1.__FrameCryptor_kind_A;
                t1 === $ && A.throwLateFieldNI("kind");
                t2.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "msgType", "event", "participantId", t3, "trackId", t4, "kind", t1, "state", "keyRatcheted", "error", "Key ratcheted ok"], t8, type$.nullable_String)));
              }
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$call$0, $async$completer);
    },
    $signature: 10
  };
  A.FrameCryptor_decodeFunction_ratchedKeyInternal.prototype = {
    call$0() {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this, newKeyBuffer, newMaterial, t1, t2, t3, t4, t5, t6, $async$temp1;
      var $async$call$0 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $async$self._box_1;
              t2 = t1.ratchetCount;
              t3 = $async$self.$this;
              t4 = t3.keyHandler;
              t5 = t4.keyOptions;
              t6 = t5.ratchetWindowSize;
              if (t2 >= t6 || t6 <= 0)
                throw A.wrapException(A.Exception_Exception("[ratchedKeyInternal] cannot ratchet anymore"));
              t2 = $async$self._box_0;
              $async$goto = 2;
              return A._asyncAwait(t4.ratchet$2(t2.currentkeySet.material, t5.ratchetSalt), $async$call$0);
            case 2:
              // returning from await.
              newKeyBuffer = $async$result;
              $async$goto = 3;
              return A._asyncAwait(t3.keyHandler.ratchetMaterial$2(t2.currentkeySet.material, J.get$buffer$x(newKeyBuffer)), $async$call$0);
            case 3:
              // returning from await.
              newMaterial = $async$result;
              t3 = t3.keyHandler;
              $async$temp1 = t2;
              $async$goto = 4;
              return A._asyncAwait(t3.deriveKeys$2(newMaterial, t3.keyOptions.ratchetSalt), $async$call$0);
            case 4:
              // returning from await.
              $async$temp1.currentkeySet = $async$result;
              ++t1.ratchetCount;
              $async$goto = 5;
              return A._asyncAwait($async$self.decryptFrameInternal.call$0(), $async$call$0);
            case 5:
              // returning from await.
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$call$0, $async$completer);
    },
    $signature: 10
  };
  A.KeyOptions.prototype = {
    toString$0(_) {
      var _this = this;
      return "KeyOptions{sharedKey: " + _this.sharedKey + ", ratchetWindowSize: " + _this.ratchetWindowSize + ", failureTolerance: " + _this.failureTolerance + ", uncryptedMagicBytes: " + A.S(_this.uncryptedMagicBytes) + ", ratchetSalt: " + A.S(_this.ratchetSalt) + "}";
    }
  };
  A.KeyProvider.prototype = {
    getParticipantKeyHandler$1(participantIdentity) {
      var t2, keys, _this = this,
        t1 = _this.keyProviderOptions;
      if (t1.sharedKey)
        return _this.getSharedKeyHandler$0();
      t2 = _this.participantKeys;
      keys = t2.$index(0, participantIdentity);
      if (keys == null) {
        keys = A.ParticipantKeyHandler$(t1, participantIdentity, _this.worker);
        t1 = _this.sharedKey;
        if (t1.length !== 0)
          keys.setKey$1(t1);
        t2.$indexSet(0, participantIdentity, keys);
      }
      return keys;
    },
    getSharedKeyHandler$0() {
      var _this = this,
        t1 = _this.sharedKeyHandler;
      return t1 == null ? _this.sharedKeyHandler = A.ParticipantKeyHandler$(_this.keyProviderOptions, "shared-key", _this.worker) : t1;
    }
  };
  A.KeySet.prototype = {};
  A.ParticipantKeyHandler.prototype = {
    decryptionFailure$0() {
      var _this = this,
        t1 = _this.keyOptions.failureTolerance;
      if (t1 < 0)
        return;
      if (++_this._decryptionFailureCount > t1) {
        $.$get$logger().log$4(B.Level_WARNING_900, "key for " + _this.participantIdentity + " is being marked as invalid", null, null);
        _this._hasValidKey = false;
      }
    },
    exportKey$1(keyIndex) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.nullable_Uint8List),
        $async$returnValue, $async$handler = 2, $async$errorStack = [], $async$self = this, key, e, exception, t1, currentMaterial, $async$exception, $async$temp1;
      var $async$exportKey$1 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1) {
          $async$errorStack.push($async$result);
          $async$goto = $async$handler;
        }
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $async$self.getKeySet$1(keyIndex);
              currentMaterial = t1 == null ? null : t1.material;
              if (currentMaterial == null) {
                $async$returnValue = null;
                // goto return
                $async$goto = 1;
                break;
              }
              $async$handler = 4;
              t1 = type$.JSObject;
              $async$temp1 = type$.NativeByteBuffer;
              $async$goto = 7;
              return A._asyncAwait(A.promiseToFuture(t1._as(t1._as(t1._as($async$self.worker.crypto).subtle).exportKey("raw", currentMaterial)), type$.nullable_Object), $async$exportKey$1);
            case 7:
              // returning from await.
              key = $async$temp1._as($async$result);
              t1 = A.NativeUint8List_NativeUint8List$view(key, 0, null);
              $async$returnValue = t1;
              // goto return
              $async$goto = 1;
              break;
              $async$handler = 2;
              // goto after finally
              $async$goto = 6;
              break;
            case 4:
              // catch
              $async$handler = 3;
              $async$exception = $async$errorStack.pop();
              e = A.unwrapException($async$exception);
              $.$get$logger().log$4(B.Level_WARNING_900, "exportKey: " + A.S(e), null, null);
              $async$returnValue = null;
              // goto return
              $async$goto = 1;
              break;
              // goto after finally
              $async$goto = 6;
              break;
            case 3:
              // uncaught
              // goto rethrow
              $async$goto = 2;
              break;
            case 6:
              // after finally
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
            case 2:
              // rethrow
              return A._asyncRethrow($async$errorStack.at(-1), $async$completer);
          }
      });
      return A._asyncStartSync($async$exportKey$1, $async$completer);
    },
    ratchetKey$1(keyIndex) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.nullable_Uint8List),
        $async$returnValue, $async$self = this, newKey, newKeySet, t1, currentMaterial;
      var $async$ratchetKey$1 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $async$self.getKeySet$1(keyIndex);
              currentMaterial = t1 == null ? null : t1.material;
              if (currentMaterial == null) {
                $async$returnValue = null;
                // goto return
                $async$goto = 1;
                break;
              }
              t1 = $async$self.keyOptions.ratchetSalt;
              $async$goto = 3;
              return A._asyncAwait($async$self.ratchet$2(currentMaterial, t1), $async$ratchetKey$1);
            case 3:
              // returning from await.
              newKey = $async$result;
              $async$goto = 5;
              return A._asyncAwait($async$self.ratchetMaterial$2(currentMaterial, B.NativeUint8List_methods.get$buffer(newKey)), $async$ratchetKey$1);
            case 5:
              // returning from await.
              $async$goto = 4;
              return A._asyncAwait($async$self.deriveKeys$2($async$result, t1), $async$ratchetKey$1);
            case 4:
              // returning from await.
              newKeySet = $async$result;
              $async$goto = 6;
              return A._asyncAwait($async$self.setKeySetFromMaterial$2(newKeySet, keyIndex == null ? $async$self.currentKeyIndex : keyIndex), $async$ratchetKey$1);
            case 6:
              // returning from await.
              $async$returnValue = newKey;
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$ratchetKey$1, $async$completer);
    },
    ratchetMaterial$2(currentMaterial, newKeyBuffer) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.JSObject),
        $async$returnValue, $async$self = this, t1;
      var $async$ratchetMaterial$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = type$.JSObject;
              $async$goto = 3;
              return A._asyncAwait(A.promiseToFuture(A.callMethod(t1._as(t1._as($async$self.worker.crypto).subtle), "importKey", ["raw", type$.NativeByteBuffer._as(newKeyBuffer), type$.Object._as(t1._as(currentMaterial.algorithm).name), false, type$.JSArray_nullable_Object._as(A.jsify(A._setArrayType(["deriveBits", "deriveKey"], type$.JSArray_String)))], t1), t1), $async$ratchetMaterial$2);
            case 3:
              // returning from await.
              $async$returnValue = $async$result;
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$ratchetMaterial$2, $async$completer);
    },
    getKeySet$1(keyIndex) {
      var t2,
        t1 = this.__ParticipantKeyHandler_cryptoKeyRing_A;
      t1 === $ && A.throwLateFieldNI("cryptoKeyRing");
      t2 = keyIndex == null ? this.currentKeyIndex : keyIndex;
      if (!(t2 >= 0 && t2 < t1.length))
        return A.ioore(t1, t2);
      return t1[t2];
    },
    setKey$2$keyIndex(key, keyIndex) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this, t1, t2, t3;
      var $async$setKey$2$keyIndex = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = type$.JSObject;
              t2 = t1._as(t1._as($async$self.worker.crypto).subtle);
              t3 = type$.String;
              t3 = A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["name", "PBKDF2"], t3, t3));
              if (t3 == null)
                t3 = type$.Object._as(t3);
              $async$goto = 4;
              return A._asyncAwait(A.promiseToFuture(A.callMethod(t2, "importKey", ["raw", key, t3, false, type$.JSArray_nullable_Object._as(A.jsify(A._setArrayType(["deriveBits", "deriveKey"], type$.JSArray_String)))], t1), t1), $async$setKey$2$keyIndex);
            case 4:
              // returning from await.
              $async$goto = 3;
              return A._asyncAwait($async$self.deriveKeys$2($async$result, $async$self.keyOptions.ratchetSalt), $async$setKey$2$keyIndex);
            case 3:
              // returning from await.
              $async$goto = 2;
              return A._asyncAwait($async$self.setKeySetFromMaterial$2($async$result, keyIndex), $async$setKey$2$keyIndex);
            case 2:
              // returning from await.
              $async$self._decryptionFailureCount = 0;
              $async$self._hasValidKey = true;
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$setKey$2$keyIndex, $async$completer);
    },
    setKey$1(key) {
      return this.setKey$2$keyIndex(key, 0);
    },
    setKeySetFromMaterial$2(keySet, keyIndex) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this, t1;
      var $async$setKeySetFromMaterial$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              $.$get$logger().log$4(B.Level_CONFIG_700, "setKeySetFromMaterial: set new key, index: " + keyIndex, null, null);
              if (keyIndex >= 0) {
                t1 = $async$self.__ParticipantKeyHandler_cryptoKeyRing_A;
                t1 === $ && A.throwLateFieldNI("cryptoKeyRing");
                $async$self.currentKeyIndex = B.JSInt_methods.$mod(keyIndex, t1.length);
              }
              t1 = $async$self.__ParticipantKeyHandler_cryptoKeyRing_A;
              t1 === $ && A.throwLateFieldNI("cryptoKeyRing");
              B.JSArray_methods.$indexSet(t1, $async$self.currentKeyIndex, keySet);
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$setKeySetFromMaterial$2, $async$completer);
    },
    deriveKeys$2(material, salt) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.KeySet),
        $async$returnValue, $async$self = this, t4, t5, t1, algorithmOptions, t2, t3, $async$temp1, $async$temp2, $async$temp3;
      var $async$deriveKeys$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = type$.JSObject;
              algorithmOptions = A.getAlgoOptions(A._asString(t1._as(material.algorithm).name), salt);
              t2 = t1._as(t1._as($async$self.worker.crypto).subtle);
              t3 = A.jsify(algorithmOptions);
              if (t3 == null)
                t3 = type$.Object._as(t3);
              t4 = type$.Object;
              t5 = A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["name", "AES-GCM", "length", 128], type$.String, t4));
              t4 = t5 == null ? t4._as(t5) : t5;
              $async$temp1 = A;
              $async$temp2 = material;
              $async$temp3 = t1;
              $async$goto = 3;
              return A._asyncAwait(A.promiseToFuture(A.callMethod(t2, "deriveKey", [t3, material, t4, false, type$.JSArray_nullable_Object._as(A.jsify(A._setArrayType(["encrypt", "decrypt"], type$.JSArray_String)))], t1), type$.nullable_Object), $async$deriveKeys$2);
            case 3:
              // returning from await.
              $async$returnValue = new $async$temp1.KeySet($async$temp2, $async$temp3._as($async$result));
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$deriveKeys$2, $async$completer);
    },
    ratchet$2(material, salt) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.Uint8List),
        $async$returnValue, $async$self = this, algorithmOptions, t1, t2, t3, $async$temp1;
      var $async$ratchet$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              algorithmOptions = A.getAlgoOptions("PBKDF2", salt);
              t1 = type$.JSObject;
              t2 = t1._as(t1._as($async$self.worker.crypto).subtle);
              t3 = A.jsify(algorithmOptions);
              if (t3 == null)
                t3 = type$.Object._as(t3);
              $async$temp1 = A;
              $async$goto = 3;
              return A._asyncAwait(A.promiseToFuture(t1._as(t2.deriveBits(t3, material, 256)), type$.NativeByteBuffer), $async$ratchet$2);
            case 3:
              // returning from await.
              $async$returnValue = $async$temp1.NativeUint8List_NativeUint8List$view($async$result, 0, null);
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$ratchet$2, $async$completer);
    },
    set$__ParticipantKeyHandler_cryptoKeyRing_A(__ParticipantKeyHandler_cryptoKeyRing_A) {
      this.__ParticipantKeyHandler_cryptoKeyRing_A = type$.List_nullable_KeySet._as(__ParticipantKeyHandler_cryptoKeyRing_A);
    }
  };
  A.SifGuard.prototype = {
    recordUserFrame$0() {
      var _this = this;
      if (_this.sifSequenceStartedAt == null)
        return;
      if (++_this.userFramesSinceSif > _this.consecutiveSifCount || Date.now() - _this.lastSifReceivedAt > 2000)
        _this.reset$0();
    },
    reset$0() {
      this.consecutiveSifCount = this.userFramesSinceSif = 0;
      this.sifSequenceStartedAt = null;
    }
  };
  A.getTrackCryptor_closure.prototype = {
    call$1(c) {
      return type$.FrameCryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.unsetCryptorParticipant_closure.prototype = {
    call$1(c) {
      return type$.FrameCryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main_closure.prototype = {
    call$1(record) {
      type$.LogRecord._as(record);
      A.printString("[" + record.loggerName + "] " + record.level.name + ": " + record.message);
    },
    $signature: 22
  };
  A.main_closure0.prototype = {
    call$1($event) {
      var t2, transformer, options, kind, participantId, trackId, codec, msgType, keyProviderId, keyProvider, cryptor, _null = null,
        t1 = type$.JSObject;
      t1._as($event);
      t2 = $.$get$logger();
      t2.log$4(B.Level_INFO_800, "Got onrtctransform event", _null, _null);
      transformer = t1._as($event.transformer);
      transformer.handled = true;
      options = t1._as(transformer.options);
      kind = A._asString(options.kind);
      participantId = A._asString(options.participantId);
      trackId = A._asString(options.trackId);
      codec = A._asStringQ(options.codec);
      msgType = A._asString(options.msgType);
      keyProviderId = A._asString(options.keyProviderId);
      keyProvider = $.keyProviders.$index(0, keyProviderId);
      if (keyProvider == null) {
        t2.log$4(B.Level_WARNING_900, "KeyProvider not found for " + keyProviderId, _null, _null);
        return;
      }
      cryptor = A.getTrackCryptor(participantId, trackId, keyProvider);
      t2 = t1._as(transformer.readable);
      t1 = t1._as(transformer.writable);
      cryptor.setupTransform$6$codec$kind$operation$readable$trackId$writable(codec == null ? _null : codec, kind, msgType, t2, trackId, t1);
    },
    $signature: 11
  };
  A.main_closure2.prototype = {
    call$1(e) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.Null),
        $async$returnValue, options, keyProviderId, t2, t3, t4, t5, t6, t7, t8, keyProviderOptions, enabled, trackId, cryptors, _i, cryptor, kind, exist, participantId, readable, writable, keyProvider, key, keyIndex, newKey, c, sifTrailer, codec, msg, msgType, msgId, t1;
      var $async$call$1 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              msg = type$.Map_dynamic_dynamic._as(A.dartify(e.data));
              msgType = msg.$index(0, "msgType");
              msgId = A._asStringQ(msg.$index(0, "msgId"));
              t1 = $.$get$logger();
              t1.log$4(B.Level_CONFIG_700, "Got message " + A.S(msgType) + ", msgId " + A.S(msgId), null, null);
            case 3:
              // switch
              switch (msgType) {
                case "keyProviderInit":
                  // goto case
                  $async$goto = 5;
                  break;
                case "keyProviderDispose":
                  // goto case
                  $async$goto = 6;
                  break;
                case "enable":
                  // goto case
                  $async$goto = 7;
                  break;
                case "decode":
                  // goto case
                  $async$goto = 8;
                  break;
                case "encode":
                  // goto case
                  $async$goto = 9;
                  break;
                case "removeTransform":
                  // goto case
                  $async$goto = 10;
                  break;
                case "setKey":
                  // goto case
                  $async$goto = 11;
                  break;
                case "setSharedKey":
                  // goto case
                  $async$goto = 12;
                  break;
                case "ratchetKey":
                  // goto case
                  $async$goto = 13;
                  break;
                case "ratchetSharedKey":
                  // goto case
                  $async$goto = 14;
                  break;
                case "setKeyIndex":
                  // goto case
                  $async$goto = 15;
                  break;
                case "exportKey":
                  // goto case
                  $async$goto = 16;
                  break;
                case "exportSharedKey":
                  // goto case
                  $async$goto = 17;
                  break;
                case "setSifTrailer":
                  // goto case
                  $async$goto = 18;
                  break;
                case "updateCodec":
                  // goto case
                  $async$goto = 19;
                  break;
                case "dispose":
                  // goto case
                  $async$goto = 20;
                  break;
                default:
                  // goto default
                  $async$goto = 21;
                  break;
              }
              break;
            case 5:
              // case
              options = msg.$index(0, "keyOptions");
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              t2 = J.getInterceptor$asx(options);
              t3 = A._asBool(t2.$index(options, "sharedKey"));
              t4 = new Uint8Array(A._ensureNativeList(B.C_Base64Decoder.convert$1(A._asString(t2.$index(options, "ratchetSalt")))));
              t5 = A._asInt(t2.$index(options, "ratchetWindowSize"));
              t6 = t2.$index(options, "failureTolerance");
              t6 = A._asInt(t6 == null ? -1 : t6);
              t7 = t2.$index(options, "uncryptedMagicBytes") != null ? new Uint8Array(A._ensureNativeList(B.C_Base64Decoder.convert$1(A._asString(t2.$index(options, "uncryptedMagicBytes"))))) : null;
              t8 = t2.$index(options, "keyRingSize");
              t8 = A._asInt(t8 == null ? 16 : t8);
              t2 = t2.$index(options, "discardFrameWhenCryptorNotReady");
              keyProviderOptions = new A.KeyOptions(t3, t4, t5, t6, t7, t8, A._asBool(t2 == null ? false : t2));
              t1.log$4(B.Level_CONFIG_700, "Init with keyProviderOptions:\n " + keyProviderOptions.toString$0(0), null, null);
              t1 = self;
              t2 = type$.JSObject;
              t3 = t2._as(t1.self);
              t4 = type$.String;
              t5 = new Uint8Array(0);
              $.keyProviders.$indexSet(0, keyProviderId, new A.KeyProvider(t3, keyProviderOptions, A.LinkedHashMap_LinkedHashMap$_empty(t4, type$.ParticipantKeyHandler), t5));
              t2._as(t1.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "init", "msgId", msgId, "msgType", "response"], t4, type$.nullable_String)));
              // goto after switch
              $async$goto = 4;
              break;
            case 6:
              // case
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              t1.log$4(B.Level_CONFIG_700, "Dispose keyProvider " + keyProviderId, null, null);
              $.keyProviders.remove$1(0, keyProviderId);
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "dispose", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
              // goto after switch
              $async$goto = 4;
              break;
            case 7:
              // case
              enabled = A._asBool(msg.$index(0, "enabled"));
              trackId = A._asString(msg.$index(0, "trackId"));
              t2 = $.participantCryptors;
              t3 = A._arrayInstanceType(t2);
              t4 = t3._eval$1("WhereIterable<1>");
              cryptors = A.List_List$of(new A.WhereIterable(t2, t3._eval$1("bool(1)")._as(new A.main__closure(trackId)), t4), true, t4._eval$1("Iterable.E"));
              for (t2 = cryptors.length, t3 = "" + enabled, t4 = "Set enable " + t3 + " for trackId ", t5 = "setEnabled[" + t3 + string$.___las, _i = 0; _i < t2; ++_i) {
                cryptor = cryptors[_i];
                t1.log$4(B.Level_CONFIG_700, t4 + cryptor.trackId, null, null);
                if (cryptor.lastError !== B.CryptorError_1) {
                  t1.log$4(B.Level_INFO_800, t5, null, null);
                  cryptor.lastError = B.CryptorError_0;
                }
                t1.log$4(B.Level_CONFIG_700, "setEnabled for " + A.S(cryptor.participantIdentity) + ", enabled: " + t3, null, null);
                cryptor._enabled = enabled;
              }
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorEnabled", "enable", enabled, "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_Object)));
              // goto after switch
              $async$goto = 4;
              break;
            case 8:
              // case
            case 9:
              // case
              kind = msg.$index(0, "kind");
              exist = A._asBool(msg.$index(0, "exist"));
              participantId = A._asString(msg.$index(0, "participantId"));
              trackId = msg.$index(0, "trackId");
              t2 = type$.JSObject;
              readable = t2._as(msg.$index(0, "readableStream"));
              writable = t2._as(msg.$index(0, "writableStream"));
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              t1.log$4(B.Level_CONFIG_700, "SetupTransform for kind " + A.S(kind) + ", trackId " + A.S(trackId) + ", participantId " + participantId + ", " + J.get$runtimeType$(readable).toString$0(0) + " " + J.get$runtimeType$(writable).toString$0(0) + "}", null, null);
              keyProvider = $.keyProviders.$index(0, keyProviderId);
              if (keyProvider == null) {
                t1.log$4(B.Level_WARNING_900, "KeyProvider not found for " + keyProviderId, null, null);
                t2._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorSetup", "participantId", participantId, "trackId", trackId, "exist", exist, "operation", msgType, "error", "KeyProvider not found", "msgId", msgId, "msgType", "response"], type$.String, type$.dynamic)));
                // goto return
                $async$goto = 1;
                break;
              }
              A._asString(trackId);
              cryptor = A.getTrackCryptor(participantId, trackId, keyProvider);
              A._asString(msgType);
              $async$goto = 22;
              return A._asyncAwait(cryptor.setupTransform$5$kind$operation$readable$trackId$writable(A._asString(kind), msgType, readable, trackId, writable), $async$call$1);
            case 22:
              // returning from await.
              t2._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorSetup", "participantId", participantId, "trackId", trackId, "exist", exist, "operation", msgType, "msgId", msgId, "msgType", "response"], type$.String, type$.dynamic)));
              cryptor.lastError = B.CryptorError_0;
              // goto after switch
              $async$goto = 4;
              break;
            case 10:
              // case
              trackId = A._asString(msg.$index(0, "trackId"));
              t1.log$4(B.Level_CONFIG_700, "Removing trackId " + trackId, null, null);
              A.unsetCryptorParticipant(trackId);
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorRemoved", "trackId", trackId, "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
              // goto after switch
              $async$goto = 4;
              break;
            case 11:
              // case
            case 12:
              // case
              key = new Uint8Array(A._ensureNativeList(B.C_Base64Decoder.convert$1(A._asString(msg.$index(0, "key")))));
              keyIndex = A._asInt(msg.$index(0, "keyIndex"));
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              keyProvider = $.keyProviders.$index(0, keyProviderId);
              if (keyProvider == null) {
                t1.log$4(B.Level_WARNING_900, "KeyProvider not found for " + keyProviderId, null, null);
                type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setKey", "error", "KeyProvider not found", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
                // goto return
                $async$goto = 1;
                break;
              }
              t2 = keyProvider.keyProviderOptions.sharedKey;
              t3 = "" + keyIndex;
              $async$goto = t2 ? 23 : 25;
              break;
            case 23:
              // then
              t1.log$4(B.Level_CONFIG_700, "Set SharedKey keyIndex " + t3, null, null);
              t1.log$4(B.Level_INFO_800, "setting shared key", null, null);
              keyProvider.sharedKey = key;
              keyProvider.getSharedKeyHandler$0().setKey$2$keyIndex(key, keyIndex);
              // goto join
              $async$goto = 24;
              break;
            case 25:
              // else
              participantId = A._asString(msg.$index(0, "participantId"));
              t1.log$4(B.Level_CONFIG_700, "Set key for participant " + participantId + ", keyIndex " + t3, null, null);
              $async$goto = 26;
              return A._asyncAwait(keyProvider.getParticipantKeyHandler$1(participantId).setKey$2$keyIndex(key, keyIndex), $async$call$1);
            case 26:
              // returning from await.
            case 24:
              // join
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setKey", "participantId", msg.$index(0, "participantId"), "sharedKey", t2, "keyIndex", keyIndex, "msgId", msgId, "msgType", "response"], type$.String, type$.dynamic)));
              // goto after switch
              $async$goto = 4;
              break;
            case 13:
              // case
            case 14:
              // case
              keyIndex = msg.$index(0, "keyIndex");
              participantId = A._asString(msg.$index(0, "participantId"));
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              keyProvider = $.keyProviders.$index(0, keyProviderId);
              if (keyProvider == null) {
                t1.log$4(B.Level_WARNING_900, "KeyProvider not found for " + keyProviderId, null, null);
                type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setKey", "error", "KeyProvider not found", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
                // goto return
                $async$goto = 1;
                break;
              }
              t2 = keyProvider.keyProviderOptions.sharedKey;
              $async$goto = t2 ? 27 : 29;
              break;
            case 27:
              // then
              t1.log$4(B.Level_CONFIG_700, "RatchetKey for SharedKey, keyIndex " + A.S(keyIndex), null, null);
              $async$goto = 30;
              return A._asyncAwait(keyProvider.getSharedKeyHandler$0().ratchetKey$1(A._asIntQ(keyIndex)), $async$call$1);
            case 30:
              // returning from await.
              newKey = $async$result;
              // goto join
              $async$goto = 28;
              break;
            case 29:
              // else
              t1.log$4(B.Level_CONFIG_700, "RatchetKey for participant " + participantId + ", keyIndex " + A.S(keyIndex), null, null);
              $async$goto = 31;
              return A._asyncAwait(keyProvider.getParticipantKeyHandler$1(participantId).ratchetKey$1(A._asIntQ(keyIndex)), $async$call$1);
            case 31:
              // returning from await.
              newKey = $async$result;
            case 28:
              // join
              t1 = type$.JSObject._as(self.self);
              t1.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "ratchetKey", "sharedKey", t2, "participantId", participantId, "newKey", newKey != null ? B.C_Base64Encoder.convert$1(type$.Base64Codec._eval$1("Codec.S")._as(newKey)) : "", "keyIndex", keyIndex, "msgId", msgId, "msgType", "response"], type$.String, type$.dynamic)));
              // goto after switch
              $async$goto = 4;
              break;
            case 15:
              // case
              keyIndex = msg.$index(0, "index");
              trackId = A._asString(msg.$index(0, "trackId"));
              t1.log$4(B.Level_CONFIG_700, "Setup key index for track " + trackId, null, null);
              t2 = $.participantCryptors;
              t3 = A._arrayInstanceType(t2);
              t4 = t3._eval$1("WhereIterable<1>");
              cryptors = A.List_List$of(new A.WhereIterable(t2, t3._eval$1("bool(1)")._as(new A.main__closure0(trackId)), t4), true, t4._eval$1("Iterable.E"));
              for (t2 = cryptors.length, _i = 0; _i < t2; ++_i) {
                c = cryptors[_i];
                t1.log$4(B.Level_CONFIG_700, "Set keyIndex for trackId " + c.trackId, null, null);
                A._asInt(keyIndex);
                if (c.lastError !== B.CryptorError_1) {
                  t1.log$4(B.Level_INFO_800, "setKeyIndex: lastError != CryptorError.kOk, reset state to kNew", null, null);
                  c.lastError = B.CryptorError_0;
                }
                t1.log$4(B.Level_CONFIG_700, "setKeyIndex for " + A.S(c.participantIdentity) + ", newIndex: " + keyIndex, null, null);
                c.currentKeyIndex = keyIndex;
              }
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setKeyIndex", "keyIndex", keyIndex, "msgId", msgId, "msgType", "response"], type$.String, type$.dynamic)));
              // goto after switch
              $async$goto = 4;
              break;
            case 16:
              // case
            case 17:
              // case
              keyIndex = A._asInt(msg.$index(0, "keyIndex"));
              participantId = A._asString(msg.$index(0, "participantId"));
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              keyProvider = $.keyProviders.$index(0, keyProviderId);
              if (keyProvider == null) {
                t1.log$4(B.Level_WARNING_900, "KeyProvider not found for " + keyProviderId, null, null);
                type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setKey", "error", "KeyProvider not found", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
                // goto return
                $async$goto = 1;
                break;
              }
              t2 = "" + keyIndex;
              $async$goto = keyProvider.keyProviderOptions.sharedKey ? 32 : 34;
              break;
            case 32:
              // then
              t1.log$4(B.Level_CONFIG_700, "Export SharedKey keyIndex " + t2, null, null);
              $async$goto = 35;
              return A._asyncAwait(keyProvider.getSharedKeyHandler$0().exportKey$1(keyIndex), $async$call$1);
            case 35:
              // returning from await.
              key = $async$result;
              // goto join
              $async$goto = 33;
              break;
            case 34:
              // else
              t1.log$4(B.Level_CONFIG_700, "Export key for participant " + participantId + ", keyIndex " + t2, null, null);
              $async$goto = 36;
              return A._asyncAwait(keyProvider.getParticipantKeyHandler$1(participantId).exportKey$1(keyIndex), $async$call$1);
            case 36:
              // returning from await.
              key = $async$result;
            case 33:
              // join
              t1 = type$.JSObject._as(self.self);
              t1.postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "exportKey", "participantId", participantId, "keyIndex", keyIndex, "exportedKey", key != null ? B.C_Base64Encoder.convert$1(type$.Base64Codec._eval$1("Codec.S")._as(key)) : "", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_Object)));
              // goto after switch
              $async$goto = 4;
              break;
            case 18:
              // case
              sifTrailer = new Uint8Array(A._ensureNativeList(B.C_Base64Decoder.convert$1(A._asString(msg.$index(0, "sifTrailer")))));
              keyProviderId = A._asString(msg.$index(0, "keyProviderId"));
              keyProvider = $.keyProviders.$index(0, keyProviderId);
              if (keyProvider == null) {
                t1.log$4(B.Level_WARNING_900, "KeyProvider not found for " + keyProviderId, null, null);
                type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setKey", "error", "KeyProvider not found", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
                // goto return
                $async$goto = 1;
                break;
              }
              keyProvider.keyProviderOptions.uncryptedMagicBytes = sifTrailer;
              t1.log$4(B.Level_CONFIG_700, "SetSifTrailer = " + A.S(sifTrailer), null, null);
              for (t2 = $.participantCryptors, t3 = t2.length, _i = 0; _i < t2.length; t2.length === t3 || (0, A.throwConcurrentModificationError)(t2), ++_i) {
                c = t2[_i];
                t1.log$4(B.Level_CONFIG_700, "setSifTrailer for " + A.S(c.participantIdentity) + ", magicBytes: " + A.S(sifTrailer), null, null);
                c.keyHandler.keyOptions.uncryptedMagicBytes = sifTrailer;
              }
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "setSifTrailer", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
              // goto after switch
              $async$goto = 4;
              break;
            case 19:
              // case
              codec = A._asString(msg.$index(0, "codec"));
              trackId = A._asString(msg.$index(0, "trackId"));
              t1.log$4(B.Level_CONFIG_700, "Update codec for trackId " + trackId + ", codec " + codec, null, null);
              cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.main__closure1(trackId), type$.FrameCryptor);
              if (cryptor != null) {
                if (cryptor.lastError !== B.CryptorError_1) {
                  t1.log$4(B.Level_INFO_800, "updateCodec[" + codec + string$.___las, null, null);
                  cryptor.lastError = B.CryptorError_0;
                }
                t1.log$4(B.Level_CONFIG_700, "updateCodec for " + A.S(cryptor.participantIdentity) + ", codec: " + codec, null, null);
                cryptor.codec = codec;
              }
              type$.JSObject._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "updateCodec", "msgId", msgId, "msgType", "response"], type$.String, type$.nullable_String)));
              // goto after switch
              $async$goto = 4;
              break;
            case 20:
              // case
              trackId = A._asString(msg.$index(0, "trackId"));
              t1.log$4(B.Level_CONFIG_700, "Dispose for trackId " + trackId, null, null);
              cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.main__closure2(trackId), type$.FrameCryptor);
              t1 = type$.JSObject;
              t2 = type$.String;
              t3 = type$.nullable_String;
              if (cryptor != null) {
                cryptor.lastError = B.CryptorError_8;
                t1._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorDispose", "participantId", cryptor.participantIdentity, "trackId", trackId, "msgId", msgId, "msgType", "response"], t2, t3)));
              } else
                t1._as(self.self).postMessage(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorDispose", "error", "cryptor not found", "msgId", msgId, "msgType", "response"], t2, t3)));
              // goto after switch
              $async$goto = 4;
              break;
            case 21:
              // default
              t1.log$4(B.Level_WARNING_900, "Unknown message kind " + msg.toString$0(0), null, null);
            case 4:
              // after switch
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$call$1, $async$completer);
    },
    $signature: 23
  };
  A.main__closure.prototype = {
    call$1(c) {
      return type$.FrameCryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main__closure0.prototype = {
    call$1(c) {
      return type$.FrameCryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main__closure1.prototype = {
    call$1(c) {
      return type$.FrameCryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main__closure2.prototype = {
    call$1(c) {
      return type$.FrameCryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main_closure1.prototype = {
    call$1(e) {
      this.handleMessage.call$1(type$.JSObject._as(e));
    },
    $signature: 11
  };
  (function aliases() {
    var _ = J.LegacyJavaScriptObject.prototype;
    _.super$LegacyJavaScriptObject$toString = _.toString$0;
    _ = A._BroadcastStreamController.prototype;
    _.super$_BroadcastStreamController$_addEventError = _._addEventError$0;
  })();
  (function installTearOffs() {
    var _static_1 = hunkHelpers._static_1,
      _static_0 = hunkHelpers._static_0,
      _static_2 = hunkHelpers._static_2,
      _instance_2_u = hunkHelpers._instance_2u,
      _instance_0_u = hunkHelpers._instance_0u;
    _static_1(A, "async__AsyncRun__scheduleImmediateJsOverride$closure", "_AsyncRun__scheduleImmediateJsOverride", 4);
    _static_1(A, "async__AsyncRun__scheduleImmediateWithSetImmediate$closure", "_AsyncRun__scheduleImmediateWithSetImmediate", 4);
    _static_1(A, "async__AsyncRun__scheduleImmediateWithTimer$closure", "_AsyncRun__scheduleImmediateWithTimer", 4);
    _static_0(A, "async___startMicrotaskLoop$closure", "_startMicrotaskLoop", 0);
    _static_2(A, "async___nullErrorHandler$closure", "_nullErrorHandler", 6);
    _static_0(A, "async___nullDoneHandler$closure", "_nullDoneHandler", 0);
    _instance_2_u(A._Future.prototype, "get$_completeError", "_completeError$2", 6);
    _instance_0_u(A._DoneStreamSubscription.prototype, "get$_onMicrotask", "_onMicrotask$0", 0);
    var _;
    _instance_2_u(_ = A.FrameCryptor.prototype, "get$encodeFunction", "encodeFunction$2", 9);
    _instance_2_u(_, "get$decodeFunction", "decodeFunction$2", 9);
  })();
  (function inheritance() {
    var _mixin = hunkHelpers.mixin,
      _inherit = hunkHelpers.inherit,
      _inheritMany = hunkHelpers.inheritMany;
    _inherit(A.Object, null);
    _inheritMany(A.Object, [A.JS_CONST, J.Interceptor, J.ArrayIterator, A._CopyingBytesBuilder, A.Error, A.SentinelValue, A.Iterable, A.ListIterator, A.MappedIterator, A.WhereIterator, A.FixedLengthListMixin, A.Symbol, A.MapView, A.ConstantMap, A._KeysOrValuesOrElementsIterator, A.JSInvocationMirror, A.Closure, A.TypeErrorDecoder, A.NullThrownFromJavaScriptException, A.ExceptionAndStackTrace, A._StackTrace, A._Required, A.MapBase, A.LinkedHashMapCell, A.LinkedHashMapKeyIterator, A._UnmodifiableNativeByteBufferView, A.Rti, A._FunctionParameters, A._Type, A._TimerImpl, A._AsyncAwaitCompleter, A.AsyncError, A.Stream, A._BufferingStreamSubscription, A._BroadcastStreamController, A._Completer, A._FutureListener, A._Future, A._AsyncCallbackEntry, A._DelayedEvent, A._PendingEvents, A._DoneStreamSubscription, A._StreamIterator, A._Zone, A._HashMapKeyIterator, A.ListBase, A._UnmodifiableMapMixin, A.Codec, A.Converter, A._Base64Encoder, A._Base64Decoder, A.DateTime, A._Enum, A.OutOfMemoryError, A.StackOverflowError, A._Exception, A.FormatException, A.Null, A._StringStackTrace, A.StringBuffer, A.NullRejectionException, A._JSSecureRandom, A.Level, A.LogRecord, A.Logger, A.FrameInfo, A.FrameCryptor, A.KeyOptions, A.KeyProvider, A.KeySet, A.ParticipantKeyHandler, A.SifGuard]);
    _inheritMany(J.Interceptor, [J.JSBool, J.JSNull, J.JavaScriptObject, J.JavaScriptBigInt, J.JavaScriptSymbol, J.JSNumber, J.JSString]);
    _inheritMany(J.JavaScriptObject, [J.LegacyJavaScriptObject, J.JSArray, A.NativeByteBuffer, A.NativeTypedData]);
    _inheritMany(J.LegacyJavaScriptObject, [J.PlainJavaScriptObject, J.UnknownJavaScriptObject, J.JavaScriptFunction]);
    _inherit(J.JSUnmodifiableArray, J.JSArray);
    _inheritMany(J.JSNumber, [J.JSInt, J.JSNumNotInt]);
    _inheritMany(A.Error, [A.LateError, A.TypeError, A.JsNoSuchMethodError, A.UnknownJsTypeError, A._CyclicInitializationError, A.RuntimeError, A.AssertionError, A._Error, A.ArgumentError, A.NoSuchMethodError, A.UnsupportedError, A.UnimplementedError, A.StateError, A.ConcurrentModificationError]);
    _inheritMany(A.Iterable, [A.EfficientLengthIterable, A.MappedIterable, A.WhereIterable, A._KeysOrValues]);
    _inheritMany(A.EfficientLengthIterable, [A.ListIterable, A.LinkedHashMapKeysIterable, A._HashMapKeyIterable]);
    _inherit(A.EfficientLengthMappedIterable, A.MappedIterable);
    _inherit(A.MappedListIterable, A.ListIterable);
    _inherit(A._UnmodifiableMapView_MapView__UnmodifiableMapMixin, A.MapView);
    _inherit(A.UnmodifiableMapView, A._UnmodifiableMapView_MapView__UnmodifiableMapMixin);
    _inherit(A.ConstantMapView, A.UnmodifiableMapView);
    _inherit(A.ConstantStringMap, A.ConstantMap);
    _inheritMany(A.Closure, [A.Closure2Args, A.Closure0Args, A.TearOffClosure, A.initHooks_closure, A.initHooks_closure1, A._AsyncRun__initializeScheduleImmediate_internalCallback, A._AsyncRun__initializeScheduleImmediate_closure, A._awaitOnObject_closure, A._SyncBroadcastStreamController__sendData_closure, A._Future__chainForeignFuture_closure, A._Future__propagateToListeners_handleWhenCompleteCallback_closure, A.Stream_length_closure, A.jsify__convert, A.promiseToFuture_closure, A.promiseToFuture_closure0, A.dartify_convert, A.getTrackCryptor_closure, A.unsetCryptorParticipant_closure, A.main_closure, A.main_closure0, A.main_closure2, A.main__closure, A.main__closure0, A.main__closure1, A.main__closure2, A.main_closure1]);
    _inheritMany(A.Closure2Args, [A.Primitives_functionNoSuchMethod_closure, A.initHooks_closure0, A._awaitOnObject_closure0, A._wrapJsFunctionForAsync_closure, A._Future__chainForeignFuture_closure0, A._Future__propagateToListeners_handleWhenCompleteCallback_closure0, A.MapBase_mapToString_closure, A.NoSuchMethodError_toString_closure]);
    _inherit(A.NullError, A.TypeError);
    _inheritMany(A.TearOffClosure, [A.StaticClosure, A.BoundClosure]);
    _inherit(A._AssertionError, A.AssertionError);
    _inheritMany(A.MapBase, [A.JsLinkedHashMap, A._HashMap]);
    _inheritMany(A.NativeTypedData, [A.NativeByteData, A.NativeTypedArray]);
    _inheritMany(A.NativeTypedArray, [A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin, A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin]);
    _inherit(A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin, A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin);
    _inherit(A.NativeTypedArrayOfDouble, A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin);
    _inherit(A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin, A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin);
    _inherit(A.NativeTypedArrayOfInt, A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin);
    _inheritMany(A.NativeTypedArrayOfDouble, [A.NativeFloat32List, A.NativeFloat64List]);
    _inheritMany(A.NativeTypedArrayOfInt, [A.NativeInt16List, A.NativeInt32List, A.NativeInt8List, A.NativeUint16List, A.NativeUint32List, A.NativeUint8ClampedList, A.NativeUint8List]);
    _inherit(A._TypeError, A._Error);
    _inheritMany(A.Closure0Args, [A._AsyncRun__scheduleImmediateJsOverride_internalCallback, A._AsyncRun__scheduleImmediateWithSetImmediate_internalCallback, A._TimerImpl_internalCallback, A._Future__addListener_closure, A._Future__prependListeners_closure, A._Future__chainForeignFuture_closure1, A._Future__chainCoreFuture_closure, A._Future__asyncCompleteWithValue_closure, A._Future__asyncCompleteError_closure, A._Future__propagateToListeners_handleWhenCompleteCallback, A._Future__propagateToListeners_handleValueCallback, A._Future__propagateToListeners_handleError, A.Stream_length_closure0, A._PendingEvents_schedule_closure, A._rootHandleError_closure, A._RootZone_bindCallbackGuarded_closure, A.Logger_Logger_closure, A.FrameCryptor_decodeFunction_decryptFrameInternal, A.FrameCryptor_decodeFunction_ratchedKeyInternal]);
    _inherit(A._StreamImpl, A.Stream);
    _inherit(A._ControllerStream, A._StreamImpl);
    _inherit(A._BroadcastStream, A._ControllerStream);
    _inherit(A._ControllerSubscription, A._BufferingStreamSubscription);
    _inherit(A._BroadcastSubscription, A._ControllerSubscription);
    _inherit(A._SyncBroadcastStreamController, A._BroadcastStreamController);
    _inherit(A._AsyncCompleter, A._Completer);
    _inherit(A._DelayedData, A._DelayedEvent);
    _inherit(A._RootZone, A._Zone);
    _inherit(A._IdentityHashMap, A._HashMap);
    _inherit(A.Base64Codec, A.Codec);
    _inheritMany(A.Converter, [A.Base64Encoder, A.Base64Decoder]);
    _inheritMany(A.ArgumentError, [A.RangeError, A.IndexError]);
    _inherit(A.CryptorError, A._Enum);
    _mixin(A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin, A.ListBase);
    _mixin(A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin, A.FixedLengthListMixin);
    _mixin(A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin, A.ListBase);
    _mixin(A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin, A.FixedLengthListMixin);
    _mixin(A._UnmodifiableMapView_MapView__UnmodifiableMapMixin, A._UnmodifiableMapMixin);
  })();
  var init = {
    typeUniverse: {eC: new Map(), tR: {}, eT: {}, tPV: {}, sEA: []},
    mangledGlobalNames: {int: "int", double: "double", num: "num", String: "String", bool: "bool", Null: "Null", List: "List", Object: "Object", Map: "Map"},
    mangledNames: {},
    types: ["~()", "bool(FrameCryptor)", "Null(@)", "~(@)", "~(~())", "Null()", "~(Object,StackTrace)", "Null(Object,StackTrace)", "Object?(Object?)", "Future<~>(JSObject,JSObject)", "Future<~>()", "Null(JSObject)", "~(String,@)", "@(@)", "@(@,String)", "@(String)", "Null(~())", "Null(@,StackTrace)", "~(int,@)", "~(Object?,Object?)", "~(Symbol0,@)", "Logger()", "~(LogRecord)", "Future<Null>(JSObject)"],
    interceptorsByTag: null,
    leafTags: null,
    arrayRti: Symbol("$ti")
  };
  A._Universe_addRules(init.typeUniverse, JSON.parse('{"JavaScriptFunction":"LegacyJavaScriptObject","PlainJavaScriptObject":"LegacyJavaScriptObject","UnknownJavaScriptObject":"LegacyJavaScriptObject","JSBool":{"bool":[],"TrustedGetRuntimeType":[]},"JSNull":{"Null":[],"TrustedGetRuntimeType":[]},"JavaScriptObject":{"JSObject":[]},"LegacyJavaScriptObject":{"JSObject":[]},"JSArray":{"List":["1"],"EfficientLengthIterable":["1"],"JSObject":[],"Iterable":["1"]},"JSUnmodifiableArray":{"JSArray":["1"],"List":["1"],"EfficientLengthIterable":["1"],"JSObject":[],"Iterable":["1"]},"ArrayIterator":{"Iterator":["1"]},"JSNumber":{"double":[],"num":[]},"JSInt":{"double":[],"int":[],"num":[],"TrustedGetRuntimeType":[]},"JSNumNotInt":{"double":[],"num":[],"TrustedGetRuntimeType":[]},"JSString":{"String":[],"Pattern":[],"TrustedGetRuntimeType":[]},"_CopyingBytesBuilder":{"BytesBuilder":[]},"LateError":{"Error":[]},"EfficientLengthIterable":{"Iterable":["1"]},"ListIterable":{"EfficientLengthIterable":["1"],"Iterable":["1"]},"ListIterator":{"Iterator":["1"]},"MappedIterable":{"Iterable":["2"],"Iterable.E":"2"},"EfficientLengthMappedIterable":{"MappedIterable":["1","2"],"EfficientLengthIterable":["2"],"Iterable":["2"],"Iterable.E":"2"},"MappedIterator":{"Iterator":["2"]},"MappedListIterable":{"ListIterable":["2"],"EfficientLengthIterable":["2"],"Iterable":["2"],"Iterable.E":"2","ListIterable.E":"2"},"WhereIterable":{"Iterable":["1"],"Iterable.E":"1"},"WhereIterator":{"Iterator":["1"]},"Symbol":{"Symbol0":[]},"ConstantMapView":{"UnmodifiableMapView":["1","2"],"_UnmodifiableMapView_MapView__UnmodifiableMapMixin":["1","2"],"MapView":["1","2"],"_UnmodifiableMapMixin":["1","2"],"Map":["1","2"]},"ConstantMap":{"Map":["1","2"]},"ConstantStringMap":{"ConstantMap":["1","2"],"Map":["1","2"]},"_KeysOrValues":{"Iterable":["1"],"Iterable.E":"1"},"_KeysOrValuesOrElementsIterator":{"Iterator":["1"]},"JSInvocationMirror":{"Invocation":[]},"NullError":{"TypeError":[],"Error":[]},"JsNoSuchMethodError":{"Error":[]},"UnknownJsTypeError":{"Error":[]},"_StackTrace":{"StackTrace":[]},"Closure":{"Function":[]},"Closure0Args":{"Function":[]},"Closure2Args":{"Function":[]},"TearOffClosure":{"Function":[]},"StaticClosure":{"Function":[]},"BoundClosure":{"Function":[]},"_CyclicInitializationError":{"Error":[]},"RuntimeError":{"Error":[]},"_AssertionError":{"Error":[]},"JsLinkedHashMap":{"MapBase":["1","2"],"LinkedHashMap":["1","2"],"Map":["1","2"]},"LinkedHashMapKeysIterable":{"EfficientLengthIterable":["1"],"Iterable":["1"],"Iterable.E":"1"},"LinkedHashMapKeyIterator":{"Iterator":["1"]},"NativeByteBuffer":{"JSObject":[],"ByteBuffer":[],"TrustedGetRuntimeType":[]},"NativeTypedData":{"JSObject":[]},"_UnmodifiableNativeByteBufferView":{"ByteBuffer":[]},"NativeByteData":{"ByteData":[],"JSObject":[],"TrustedGetRuntimeType":[]},"NativeTypedArray":{"JavaScriptIndexingBehavior":["1"],"JSObject":[]},"NativeTypedArrayOfDouble":{"ListBase":["double"],"NativeTypedArray":["double"],"List":["double"],"JavaScriptIndexingBehavior":["double"],"EfficientLengthIterable":["double"],"JSObject":[],"Iterable":["double"],"FixedLengthListMixin":["double"]},"NativeTypedArrayOfInt":{"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"]},"NativeFloat32List":{"Float32List":[],"ListBase":["double"],"NativeTypedArray":["double"],"List":["double"],"JavaScriptIndexingBehavior":["double"],"EfficientLengthIterable":["double"],"JSObject":[],"Iterable":["double"],"FixedLengthListMixin":["double"],"TrustedGetRuntimeType":[],"ListBase.E":"double"},"NativeFloat64List":{"Float64List":[],"ListBase":["double"],"NativeTypedArray":["double"],"List":["double"],"JavaScriptIndexingBehavior":["double"],"EfficientLengthIterable":["double"],"JSObject":[],"Iterable":["double"],"FixedLengthListMixin":["double"],"TrustedGetRuntimeType":[],"ListBase.E":"double"},"NativeInt16List":{"Int16List":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"NativeInt32List":{"Int32List":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"NativeInt8List":{"Int8List":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"NativeUint16List":{"Uint16List":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"NativeUint32List":{"Uint32List":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"NativeUint8ClampedList":{"Uint8ClampedList":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"NativeUint8List":{"Uint8List":[],"ListBase":["int"],"NativeTypedArray":["int"],"List":["int"],"JavaScriptIndexingBehavior":["int"],"EfficientLengthIterable":["int"],"JSObject":[],"Iterable":["int"],"FixedLengthListMixin":["int"],"TrustedGetRuntimeType":[],"ListBase.E":"int"},"_Error":{"Error":[]},"_TypeError":{"TypeError":[],"Error":[]},"_BufferingStreamSubscription":{"StreamSubscription":["1"],"_EventDispatch":["1"]},"AsyncError":{"Error":[]},"_BroadcastStream":{"_ControllerStream":["1"],"_StreamImpl":["1"],"Stream":["1"]},"_BroadcastSubscription":{"_ControllerSubscription":["1"],"_BufferingStreamSubscription":["1"],"StreamSubscription":["1"],"_EventDispatch":["1"]},"_BroadcastStreamController":{"StreamController":["1"],"_StreamControllerLifecycle":["1"],"_EventDispatch":["1"]},"_SyncBroadcastStreamController":{"_BroadcastStreamController":["1"],"StreamController":["1"],"_StreamControllerLifecycle":["1"],"_EventDispatch":["1"]},"_AsyncCompleter":{"_Completer":["1"]},"_Future":{"Future":["1"]},"_ControllerStream":{"_StreamImpl":["1"],"Stream":["1"]},"_ControllerSubscription":{"_BufferingStreamSubscription":["1"],"StreamSubscription":["1"],"_EventDispatch":["1"]},"_StreamImpl":{"Stream":["1"]},"_DelayedData":{"_DelayedEvent":["1"]},"_DoneStreamSubscription":{"StreamSubscription":["1"]},"_Zone":{"Zone":[]},"_RootZone":{"_Zone":[],"Zone":[]},"_HashMap":{"MapBase":["1","2"],"Map":["1","2"]},"_IdentityHashMap":{"_HashMap":["1","2"],"MapBase":["1","2"],"Map":["1","2"]},"_HashMapKeyIterable":{"EfficientLengthIterable":["1"],"Iterable":["1"],"Iterable.E":"1"},"_HashMapKeyIterator":{"Iterator":["1"]},"MapBase":{"Map":["1","2"]},"MapView":{"Map":["1","2"]},"UnmodifiableMapView":{"_UnmodifiableMapView_MapView__UnmodifiableMapMixin":["1","2"],"MapView":["1","2"],"_UnmodifiableMapMixin":["1","2"],"Map":["1","2"]},"Base64Codec":{"Codec":["List<int>","String"],"Codec.S":"List<int>"},"double":{"num":[]},"int":{"num":[]},"List":{"EfficientLengthIterable":["1"],"Iterable":["1"]},"String":{"Pattern":[]},"AssertionError":{"Error":[]},"TypeError":{"Error":[]},"ArgumentError":{"Error":[]},"RangeError":{"Error":[]},"IndexError":{"Error":[]},"NoSuchMethodError":{"Error":[]},"UnsupportedError":{"Error":[]},"UnimplementedError":{"Error":[]},"StateError":{"Error":[]},"ConcurrentModificationError":{"Error":[]},"OutOfMemoryError":{"Error":[]},"StackOverflowError":{"Error":[]},"_StringStackTrace":{"StackTrace":[]},"Int8List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Uint8List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Uint8ClampedList":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Int16List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Uint16List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Int32List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Uint32List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]},"Float32List":{"List":["double"],"EfficientLengthIterable":["double"],"Iterable":["double"]},"Float64List":{"List":["double"],"EfficientLengthIterable":["double"],"Iterable":["double"]}}'));
  A._Universe_addErasedTypes(init.typeUniverse, JSON.parse('{"EfficientLengthIterable":1,"NativeTypedArray":1,"_DelayedEvent":1,"Converter":2}'));
  var string$ = {
    Cannot: "Cannot fire new event. Controller is already firing an event",
    Error_: "Error handler must accept one Object or one Object and a StackTrace as arguments, and return a value of the returned future's type",
    ___las: "]: lastError != CryptorError.kOk, reset state to kNew",
    decode: "decodeFunction::decryptFrameInternal: decrypted: "
  };
  var type$ = (function rtii() {
    var findType = A.findType;
    return {
      $env_1_1_void: findType("@<~>"),
      AsyncError: findType("AsyncError"),
      Base64Codec: findType("Base64Codec"),
      ByteBuffer: findType("ByteBuffer"),
      ByteData: findType("ByteData"),
      ConstantMapView_Symbol_dynamic: findType("ConstantMapView<Symbol0,@>"),
      EfficientLengthIterable_dynamic: findType("EfficientLengthIterable<@>"),
      Error: findType("Error"),
      Float32List: findType("Float32List"),
      Float64List: findType("Float64List"),
      FrameCryptor: findType("FrameCryptor"),
      Function: findType("Function"),
      Future_dynamic: findType("Future<@>"),
      Future_void_Function_JSObject_JSObject: findType("Future<~>(JSObject,JSObject)"),
      Int16List: findType("Int16List"),
      Int32List: findType("Int32List"),
      Int8List: findType("Int8List"),
      Invocation: findType("Invocation"),
      Iterable_dynamic: findType("Iterable<@>"),
      Iterable_int: findType("Iterable<int>"),
      Iterable_nullable_Object: findType("Iterable<Object?>"),
      JSArray_String: findType("JSArray<String>"),
      JSArray_dynamic: findType("JSArray<@>"),
      JSArray_int: findType("JSArray<int>"),
      JSArray_nullable_Object: findType("JSArray<Object?>"),
      JSNull: findType("JSNull"),
      JSObject: findType("JSObject"),
      JavaScriptFunction: findType("JavaScriptFunction"),
      JavaScriptIndexingBehavior_dynamic: findType("JavaScriptIndexingBehavior<@>"),
      JsLinkedHashMap_Symbol_dynamic: findType("JsLinkedHashMap<Symbol0,@>"),
      KeySet: findType("KeySet"),
      List_dynamic: findType("List<@>"),
      List_int: findType("List<int>"),
      List_nullable_KeySet: findType("List<KeySet?>"),
      LogRecord: findType("LogRecord"),
      Logger: findType("Logger"),
      Map_dynamic_dynamic: findType("Map<@,@>"),
      Map_of_nullable_Object_and_nullable_Object: findType("Map<Object?,Object?>"),
      NativeByteBuffer: findType("NativeByteBuffer"),
      Null: findType("Null"),
      Object: findType("Object"),
      ParticipantKeyHandler: findType("ParticipantKeyHandler"),
      Record: findType("Record"),
      StackTrace: findType("StackTrace"),
      String: findType("String"),
      Symbol: findType("Symbol0"),
      TrustedGetRuntimeType: findType("TrustedGetRuntimeType"),
      TypeError: findType("TypeError"),
      Uint16List: findType("Uint16List"),
      Uint32List: findType("Uint32List"),
      Uint8ClampedList: findType("Uint8ClampedList"),
      Uint8List: findType("Uint8List"),
      UnknownJavaScriptObject: findType("UnknownJavaScriptObject"),
      _Future_dynamic: findType("_Future<@>"),
      _Future_int: findType("_Future<int>"),
      _IdentityHashMap_of_nullable_Object_and_nullable_Object: findType("_IdentityHashMap<Object?,Object?>"),
      _SyncBroadcastStreamController_LogRecord: findType("_SyncBroadcastStreamController<LogRecord>"),
      bool: findType("bool"),
      bool_Function_Object: findType("bool(Object)"),
      double: findType("double"),
      dynamic: findType("@"),
      dynamic_Function: findType("@()"),
      dynamic_Function_Object: findType("@(Object)"),
      dynamic_Function_Object_StackTrace: findType("@(Object,StackTrace)"),
      int: findType("int"),
      legacy_Never: findType("0&*"),
      legacy_Object: findType("Object*"),
      nullable_Future_Null: findType("Future<Null>?"),
      nullable_KeySet: findType("KeySet?"),
      nullable_Object: findType("Object?"),
      nullable_StreamController_LogRecord: findType("StreamController<LogRecord>?"),
      nullable_String: findType("String?"),
      nullable_Uint8List: findType("Uint8List?"),
      nullable__FutureListener_dynamic_dynamic: findType("_FutureListener<@,@>?"),
      nullable_void_Function: findType("~()?"),
      num: findType("num"),
      void: findType("~"),
      void_Function: findType("~()"),
      void_Function_Object: findType("~(Object)"),
      void_Function_Object_StackTrace: findType("~(Object,StackTrace)")
    };
  })();
  (function constants() {
    var makeConstList = hunkHelpers.makeConstList;
    B.Interceptor_methods = J.Interceptor.prototype;
    B.JSArray_methods = J.JSArray.prototype;
    B.JSInt_methods = J.JSInt.prototype;
    B.JSString_methods = J.JSString.prototype;
    B.JavaScriptFunction_methods = J.JavaScriptFunction.prototype;
    B.JavaScriptObject_methods = J.JavaScriptObject.prototype;
    B.NativeByteData_methods = A.NativeByteData.prototype;
    B.NativeUint8List_methods = A.NativeUint8List.prototype;
    B.PlainJavaScriptObject_methods = J.PlainJavaScriptObject.prototype;
    B.UnknownJavaScriptObject_methods = J.UnknownJavaScriptObject.prototype;
    B.C_Base64Decoder = new A.Base64Decoder();
    B.C_Base64Encoder = new A.Base64Encoder();
    B.C_JS_CONST = function getTagFallback(o) {
  var s = Object.prototype.toString.call(o);
  return s.substring(8, s.length - 1);
};
    B.C_JS_CONST0 = function() {
  var toStringFunction = Object.prototype.toString;
  function getTag(o) {
    var s = toStringFunction.call(o);
    return s.substring(8, s.length - 1);
  }
  function getUnknownTag(object, tag) {
    if (/^HTML[A-Z].*Element$/.test(tag)) {
      var name = toStringFunction.call(object);
      if (name == "[object Object]") return null;
      return "HTMLElement";
    }
  }
  function getUnknownTagGenericBrowser(object, tag) {
    if (object instanceof HTMLElement) return "HTMLElement";
    return getUnknownTag(object, tag);
  }
  function prototypeForTag(tag) {
    if (typeof window == "undefined") return null;
    if (typeof window[tag] == "undefined") return null;
    var constructor = window[tag];
    if (typeof constructor != "function") return null;
    return constructor.prototype;
  }
  function discriminator(tag) { return null; }
  var isBrowser = typeof HTMLElement == "function";
  return {
    getTag: getTag,
    getUnknownTag: isBrowser ? getUnknownTagGenericBrowser : getUnknownTag,
    prototypeForTag: prototypeForTag,
    discriminator: discriminator };
};
    B.C_JS_CONST6 = function(getTagFallback) {
  return function(hooks) {
    if (typeof navigator != "object") return hooks;
    var userAgent = navigator.userAgent;
    if (typeof userAgent != "string") return hooks;
    if (userAgent.indexOf("DumpRenderTree") >= 0) return hooks;
    if (userAgent.indexOf("Chrome") >= 0) {
      function confirm(p) {
        return typeof window == "object" && window[p] && window[p].name == p;
      }
      if (confirm("Window") && confirm("HTMLElement")) return hooks;
    }
    hooks.getTag = getTagFallback;
  };
};
    B.C_JS_CONST1 = function(hooks) {
  if (typeof dartExperimentalFixupGetTag != "function") return hooks;
  hooks.getTag = dartExperimentalFixupGetTag(hooks.getTag);
};
    B.C_JS_CONST5 = function(hooks) {
  if (typeof navigator != "object") return hooks;
  var userAgent = navigator.userAgent;
  if (typeof userAgent != "string") return hooks;
  if (userAgent.indexOf("Firefox") == -1) return hooks;
  var getTag = hooks.getTag;
  var quickMap = {
    "BeforeUnloadEvent": "Event",
    "DataTransfer": "Clipboard",
    "GeoGeolocation": "Geolocation",
    "Location": "!Location",
    "WorkerMessageEvent": "MessageEvent",
    "XMLDocument": "!Document"};
  function getTagFirefox(o) {
    var tag = getTag(o);
    return quickMap[tag] || tag;
  }
  hooks.getTag = getTagFirefox;
};
    B.C_JS_CONST4 = function(hooks) {
  if (typeof navigator != "object") return hooks;
  var userAgent = navigator.userAgent;
  if (typeof userAgent != "string") return hooks;
  if (userAgent.indexOf("Trident/") == -1) return hooks;
  var getTag = hooks.getTag;
  var quickMap = {
    "BeforeUnloadEvent": "Event",
    "DataTransfer": "Clipboard",
    "HTMLDDElement": "HTMLElement",
    "HTMLDTElement": "HTMLElement",
    "HTMLPhraseElement": "HTMLElement",
    "Position": "Geoposition"
  };
  function getTagIE(o) {
    var tag = getTag(o);
    var newTag = quickMap[tag];
    if (newTag) return newTag;
    if (tag == "Object") {
      if (window.DataView && (o instanceof window.DataView)) return "DataView";
    }
    return tag;
  }
  function prototypeForTagIE(tag) {
    var constructor = window[tag];
    if (constructor == null) return null;
    return constructor.prototype;
  }
  hooks.getTag = getTagIE;
  hooks.prototypeForTag = prototypeForTagIE;
};
    B.C_JS_CONST2 = function(hooks) {
  var getTag = hooks.getTag;
  var prototypeForTag = hooks.prototypeForTag;
  function getTagFixed(o) {
    var tag = getTag(o);
    if (tag == "Document") {
      if (!!o.xmlVersion) return "!Document";
      return "!HTMLDocument";
    }
    return tag;
  }
  function prototypeForTagFixed(tag) {
    if (tag == "Document") return null;
    return prototypeForTag(tag);
  }
  hooks.getTag = getTagFixed;
  hooks.prototypeForTag = prototypeForTagFixed;
};
    B.C_JS_CONST3 = function(hooks) { return hooks; }
;
    B.C_OutOfMemoryError = new A.OutOfMemoryError();
    B.C_SentinelValue = new A.SentinelValue();
    B.C__Required = new A._Required();
    B.C__RootZone = new A._RootZone();
    B.C__StringStackTrace = new A._StringStackTrace();
    B.CryptorError_0 = new A.CryptorError("kNew");
    B.CryptorError_1 = new A.CryptorError("kOk");
    B.CryptorError_2 = new A.CryptorError("kDecryptError");
    B.CryptorError_3 = new A.CryptorError("kEncryptError");
    B.CryptorError_5 = new A.CryptorError("kMissingKey");
    B.CryptorError_6 = new A.CryptorError("kKeyRatcheted");
    B.CryptorError_7 = new A.CryptorError("kInternalError");
    B.CryptorError_8 = new A.CryptorError("kDisposed");
    B.Level_CONFIG_700 = new A.Level("CONFIG", 700);
    B.Level_FINER_400 = new A.Level("FINER", 400);
    B.Level_FINE_500 = new A.Level("FINE", 500);
    B.Level_INFO_800 = new A.Level("INFO", 800);
    B.Level_WARNING_900 = new A.Level("WARNING", 900);
    B.List_empty = A._setArrayType(makeConstList([]), type$.JSArray_dynamic);
    B.Object_empty = {};
    B.Map_empty = new A.ConstantStringMap(B.Object_empty, [], A.findType("ConstantStringMap<Symbol0,@>"));
    B.Symbol_call = new A.Symbol("call");
    B.Type_ByteBuffer_rqD = A.typeLiteral("ByteBuffer");
    B.Type_ByteData_9dB = A.typeLiteral("ByteData");
    B.Type_Float32List_9Kz = A.typeLiteral("Float32List");
    B.Type_Float64List_9Kz = A.typeLiteral("Float64List");
    B.Type_Int16List_s5h = A.typeLiteral("Int16List");
    B.Type_Int32List_O8Z = A.typeLiteral("Int32List");
    B.Type_Int8List_rFV = A.typeLiteral("Int8List");
    B.Type_JSObject_ttY = A.typeLiteral("JSObject");
    B.Type_Object_A4p = A.typeLiteral("Object");
    B.Type_Uint16List_kmP = A.typeLiteral("Uint16List");
    B.Type_Uint32List_kmP = A.typeLiteral("Uint32List");
    B.Type_Uint8ClampedList_04U = A.typeLiteral("Uint8ClampedList");
    B.Type_Uint8List_8Eb = A.typeLiteral("Uint8List");
  })();
  (function staticFields() {
    $._JS_INTEROP_INTERCEPTOR_TAG = null;
    $.toStringVisiting = A._setArrayType([], A.findType("JSArray<Object>"));
    $.Primitives__identityHashCodeProperty = null;
    $.BoundClosure__receiverFieldNameCache = null;
    $.BoundClosure__interceptorFieldNameCache = null;
    $.getTagFunction = null;
    $.alternateTagFunction = null;
    $.prototypeForTagFunction = null;
    $.dispatchRecordsForInstanceTags = null;
    $.interceptorsForUncacheableTags = null;
    $.initNativeDispatchFlag = null;
    $._nextCallback = null;
    $._lastCallback = null;
    $._lastPriorityCallback = null;
    $._isInCallbackLoop = false;
    $.Zone__current = B.C__RootZone;
    $.LogRecord__nextNumber = 0;
    $.Logger__loggers = A.LinkedHashMap_LinkedHashMap$_empty(type$.String, type$.Logger);
    $.participantCryptors = A._setArrayType([], A.findType("JSArray<FrameCryptor>"));
    $.keyProviders = A.LinkedHashMap_LinkedHashMap$_empty(type$.String, A.findType("KeyProvider"));
  })();
  (function lazyInitializers() {
    var _lazyFinal = hunkHelpers.lazyFinal,
      _lazy = hunkHelpers.lazy;
    _lazyFinal($, "DART_CLOSURE_PROPERTY_NAME", "$get$DART_CLOSURE_PROPERTY_NAME", () => A.getIsolateAffinityTag("_$dart_dartClosure"));
    _lazyFinal($, "_CopyingBytesBuilder__emptyList", "$get$_CopyingBytesBuilder__emptyList", () => A.NativeUint8List_NativeUint8List(0));
    _lazyFinal($, "TypeErrorDecoder_noSuchMethodPattern", "$get$TypeErrorDecoder_noSuchMethodPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn({
      toString: function() {
        return "$receiver$";
      }
    })));
    _lazyFinal($, "TypeErrorDecoder_notClosurePattern", "$get$TypeErrorDecoder_notClosurePattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn({$method$: null,
      toString: function() {
        return "$receiver$";
      }
    })));
    _lazyFinal($, "TypeErrorDecoder_nullCallPattern", "$get$TypeErrorDecoder_nullCallPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn(null)));
    _lazyFinal($, "TypeErrorDecoder_nullLiteralCallPattern", "$get$TypeErrorDecoder_nullLiteralCallPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      var $argumentsExpr$ = "$arguments$";
      try {
        null.$method$($argumentsExpr$);
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "TypeErrorDecoder_undefinedCallPattern", "$get$TypeErrorDecoder_undefinedCallPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn(void 0)));
    _lazyFinal($, "TypeErrorDecoder_undefinedLiteralCallPattern", "$get$TypeErrorDecoder_undefinedLiteralCallPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      var $argumentsExpr$ = "$arguments$";
      try {
        (void 0).$method$($argumentsExpr$);
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "TypeErrorDecoder_nullPropertyPattern", "$get$TypeErrorDecoder_nullPropertyPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokePropertyErrorOn(null)));
    _lazyFinal($, "TypeErrorDecoder_nullLiteralPropertyPattern", "$get$TypeErrorDecoder_nullLiteralPropertyPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      try {
        null.$method$;
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "TypeErrorDecoder_undefinedPropertyPattern", "$get$TypeErrorDecoder_undefinedPropertyPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokePropertyErrorOn(void 0)));
    _lazyFinal($, "TypeErrorDecoder_undefinedLiteralPropertyPattern", "$get$TypeErrorDecoder_undefinedLiteralPropertyPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      try {
        (void 0).$method$;
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "_AsyncRun__scheduleImmediateClosure", "$get$_AsyncRun__scheduleImmediateClosure", () => A._AsyncRun__initializeScheduleImmediate());
    _lazyFinal($, "_Base64Decoder__inverseAlphabet", "$get$_Base64Decoder__inverseAlphabet", () => new Int8Array(A._ensureNativeList(A._setArrayType([-2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -1, -2, -2, -2, -2, -2, 62, -2, 62, -2, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -2, -2, -2, -1, -2, -2, -2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -2, -2, -2, -2, 63, -2, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -2, -2, -2, -2, -2], type$.JSArray_int))));
    _lazy($, "_Base64Decoder__emptyBuffer", "$get$_Base64Decoder__emptyBuffer", () => A.NativeUint8List_NativeUint8List(0));
    _lazyFinal($, "_hashSeed", "$get$_hashSeed", () => A.objectHashCode(B.Type_Object_A4p));
    _lazyFinal($, "Random__secureRandom", "$get$Random__secureRandom", () => {
      var t1 = new A._JSSecureRandom(A.NativeByteData_NativeByteData(8));
      t1._JSSecureRandom$0();
      return t1;
    });
    _lazyFinal($, "Logger_root", "$get$Logger_root", () => A.Logger_Logger(""));
    _lazyFinal($, "logger", "$get$logger", () => A.Logger_Logger("E2EE.Worker"));
  })();
  (function nativeSupport() {
    !function() {
      var intern = function(s) {
        var o = {};
        o[s] = 1;
        return Object.keys(hunkHelpers.convertToFastObject(o))[0];
      };
      init.getIsolateTag = function(name) {
        return intern("___dart_" + name + init.isolateTag);
      };
      var tableProperty = "___dart_isolate_tags_";
      var usedProperties = Object[tableProperty] || (Object[tableProperty] = Object.create(null));
      var rootProperty = "_ZxYxX";
      for (var i = 0;; i++) {
        var property = intern(rootProperty + "_" + i + "_");
        if (!(property in usedProperties)) {
          usedProperties[property] = 1;
          init.isolateTag = property;
          break;
        }
      }
      init.dispatchPropertyName = init.getIsolateTag("dispatch_record");
    }();
    hunkHelpers.setOrUpdateInterceptorsByTag({ArrayBuffer: A.NativeByteBuffer, ArrayBufferView: A.NativeTypedData, DataView: A.NativeByteData, Float32Array: A.NativeFloat32List, Float64Array: A.NativeFloat64List, Int16Array: A.NativeInt16List, Int32Array: A.NativeInt32List, Int8Array: A.NativeInt8List, Uint16Array: A.NativeUint16List, Uint32Array: A.NativeUint32List, Uint8ClampedArray: A.NativeUint8ClampedList, CanvasPixelArray: A.NativeUint8ClampedList, Uint8Array: A.NativeUint8List});
    hunkHelpers.setOrUpdateLeafTags({ArrayBuffer: true, ArrayBufferView: false, DataView: true, Float32Array: true, Float64Array: true, Int16Array: true, Int32Array: true, Int8Array: true, Uint16Array: true, Uint32Array: true, Uint8ClampedArray: true, CanvasPixelArray: true, Uint8Array: false});
    A.NativeTypedArray.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A.NativeTypedArrayOfDouble.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A.NativeTypedArrayOfInt.$nativeSuperclassTag = "ArrayBufferView";
  })();
  Function.prototype.call$1 = function(a) {
    return this(a);
  };
  Function.prototype.call$0 = function() {
    return this();
  };
  Function.prototype.call$2 = function(a, b) {
    return this(a, b);
  };
  Function.prototype.call$3 = function(a, b, c) {
    return this(a, b, c);
  };
  Function.prototype.call$4 = function(a, b, c, d) {
    return this(a, b, c, d);
  };
  Function.prototype.call$1$1 = function(a) {
    return this(a);
  };
  convertAllToFastObject(holders);
  convertToFastObject($);
  (function(callback) {
    if (typeof document === "undefined") {
      callback(null);
      return;
    }
    if (typeof document.currentScript != "undefined") {
      callback(document.currentScript);
      return;
    }
    var scripts = document.scripts;
    function onLoad(event) {
      for (var i = 0; i < scripts.length; ++i) {
        scripts[i].removeEventListener("load", onLoad, false);
      }
      callback(event.target);
    }
    for (var i = 0; i < scripts.length; ++i) {
      scripts[i].addEventListener("load", onLoad, false);
    }
  })(function(currentScript) {
    init.currentScript = currentScript;
    var callMain = A.main;
    if (typeof dartMainRunner === "function") {
      dartMainRunner(callMain, []);
    } else {
      callMain([]);
    }
  });
})();

//# sourceMappingURL=e2ee.worker.dart.js.map
