# Copyright 2024 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  dart-format-and-analyze-check:
    name: Dart Format/Analyze/Test Check
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '17.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Dart Format Check
        run: dart format lib/ test/ --set-exit-if-changed
      - name: Import Sorter Check
        run: dart run import_sorter:main --no-comments --exit-if-changed
      - name: Check version consistency
        run: dart run scripts/check_version.dart
      - name: <PERSON>t Analyze Check
        run: flutter analyze
      - name: Dart Test Check
        run: flutter test

  build-for-android:
    name: Build for Flutter Android
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '17.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for Android
        working-directory: ./example
        run: flutter build apk

  build-for-ios:
    name: Build for Flutter iOS
    runs-on: macos-latest

    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for iOS
        working-directory: ./example
        run: flutter build ios --release --no-codesign

  build-for-windows:
    name: Build for Flutter Windows
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v1
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for Windows
        working-directory: ./example
        run: flutter build windows --release

  build-for-macos:
    name: Build for Flutter macOS
    runs-on: macos-latest

    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v1
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for macOS
        working-directory: ./example
        run: flutter build macos --release

  build-for-linux:
    name: Build for Flutter Linux
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '12.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Run apt update
        run: sudo apt-get update
      - name: Install ninja-build libgtk-3-dev
        run: sudo apt-get install -y ninja-build libgtk-3-dev
      - name: Build for Linux
        working-directory: ./example
        run: flutter build linux

  build-for-web:
    name: Build for Flutter Web
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '12.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for Web
        working-directory: ./example
        run: flutter build web

  build-for-web-wasm:
    name: Build for Flutter Web WASM
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '12.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for Web
        working-directory: ./example
        run: flutter build web --wasm