name: deploy web on github-page
on:
  push:
    branches: [main]

jobs:
  build:
    name: Build Web
    env:
      my_secret: ${{secrets.commit_secret}}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: subosito/flutter-action@v1
        with:
          channel: "stable"
      - run: flutter config --enable-web
      - run: flutter clean
      - run: flutter pub get
      - run: |
          dart compile js ./web/e2ee.worker.dart -o ./example/web/e2ee.worker.dart.js
          cd example
          flutter build web --release --base-href /client-sdk-flutter/
          cd build/web
          git init
          git config --global user.email <EMAIL>
          git config --global user.name cloudwebrtc
          git status
          git remote add origin https://${{secrets.commit_secret}}@github.com/livekit/client-sdk-flutter.git
          git checkout -b gh-pages
          git add --all
          git commit -m "update"
          git push origin gh-pages -f
